import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { catchError, finalize } from 'rxjs/operators';
import { of, forkJoin } from 'rxjs';
import Swal from 'sweetalert2';
import { Chart, registerables } from 'chart.js';

@Component({
  selector: 'app-graficos-ventas',
  templateUrl: './graficos-ventas.component.html',
  styleUrls: ['./graficos-ventas.component.scss']
})
export class GraficosVentasComponent implements OnInit, AfterViewInit {
  @ViewChild('chartAsesores') chartAsesoresRef!: ElementRef;
  @ViewChild('chartCoordinadores') chartCoordinadoresRef!: ElementRef;

  // Formularios
  filterForm: FormGroup;
  metaForm: FormGroup;

  // Estados de carga
  loading = false;
  loadingAsesores = false;
  loadingMetas = false;
  guardandoMeta = false;

  // Datos para gráficos
  chartData: any[] = [];

  // Instancias de gráficos
  chartAsesores: Chart<any, any, any> | null = null;
  chartCoordinadores: Chart<any, any, any> | null = null;

  // Estados de modales
  mostrarModalAsesores = false;
  mostrarModalMetas = false;
  mostrarModalRegistroMeta = false;

  // Datos para modales
  asesores: any[] = [];
  asesoresFiltrados: any[] = [];
  metas: any[] = [];
  asesorSeleccionado: any = null;

  // Paginación y búsqueda
  paginaActual = 1;
  elementosPorPagina = 10;
  terminoBusqueda = '';
  totalPaginas = 1;

  // URL base para las APIs
  private baseUrl = 'https://apisozarusac.com';

  // Referencia a Math para usar en el template
  Math = Math;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient
  ) {
    // Inicializar el formulario de filtros con fechas por defecto
    const today = new Date();

    // Establecer fecha de inicio como el primer día del mes actual
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    this.filterForm = this.fb.group({
      fechaInicio: [firstDayOfMonth],
      fechaFin: [today]
    });

    // Inicializar el formulario de metas
    this.metaForm = this.fb.group({
      fechaInicio: ['', Validators.required],
      fechaFin: ['', Validators.required],
      cantidadMeta: ['', [Validators.required, Validators.min(1)]]
    });
  }

  ngOnInit(): void {
    // Registrar todos los componentes de Chart.js
    Chart.register(...registerables);

    // No cargamos datos aquí, lo haremos en ngAfterViewInit
    // para asegurarnos de que los elementos canvas estén disponibles
  }

  ngAfterViewInit(): void {
    // Asegurarnos de que los elementos canvas estén listos antes de cargar datos
    setTimeout(() => {
      // Verificar si los elementos canvas existen
      if (this.chartAsesoresRef && this.chartCoordinadoresRef) {
        console.log('Canvas de gráficos encontrados en ngAfterViewInit');
        // Cargar datos iniciales
        this.cargarDatosGrafico();
      } else {
        console.error('Canvas de gráficos no encontrados en ngAfterViewInit');
      }
    }, 100);
  }

  // Método para cargar los datos del gráfico según los filtros
  cargarDatosGrafico(): void {
    this.loading = true;
    const filtros = this.filterForm.value;

    // Inicializar chartData como un array vacío para evitar problemas
    this.chartData = [];

    console.log('Cargando datos con filtros:', filtros);

    // Construir los parámetros de consulta
    const params = new URLSearchParams();

    // Usar fechas por defecto si no se han seleccionado
    const fechaInicio = filtros.fechaInicio ? new Date(filtros.fechaInicio) : new Date();
    const fechaFin = filtros.fechaFin ? new Date(filtros.fechaFin) : new Date(new Date().setDate(new Date().getDate() + 1));

    params.append('fecha_inicio', fechaInicio.toISOString().split('T')[0]);
    params.append('fecha_fin', fechaFin.toISOString().split('T')[0]);
    params.append('limite', '10'); // Mostrar los 10 mejores

    console.log('Parámetros de consulta:', {
      fecha_inicio: fechaInicio.toISOString().split('T')[0],
      fecha_fin: fechaFin.toISOString().split('T')[0]
    });

    // Verificar si los elementos canvas existen
    const canvasExisten = this.chartAsesoresRef && this.chartCoordinadoresRef;
    if (!canvasExisten) {
      console.warn('Los elementos canvas no están disponibles todavía. Esperando 500ms...');

      // Esperar un momento para que los elementos canvas estén disponibles
      setTimeout(() => {
        if (this.chartAsesoresRef && this.chartCoordinadoresRef) {
          console.log('Canvas encontrados después de esperar. Procediendo con la carga de datos.');
          this.cargarDatosDesdeAPI(params);
        } else {
          console.error('Canvas no encontrados después de esperar. Intentando una última vez...');

          // Último intento después de otro retraso
          setTimeout(() => {
            console.log('Último intento de cargar datos.');
            this.cargarDatosDesdeAPI(params);
          }, 500);
        }
      }, 500);
    } else {
      // Los canvas ya existen, proceder con la carga de datos
      console.log('Canvas encontrados. Procediendo con la carga de datos.');
      this.cargarDatosDesdeAPI(params);
    }
  }

  // Método auxiliar para cargar datos desde la API
  private cargarDatosDesdeAPI(params: URLSearchParams): void {
    // Usar forkJoin para hacer ambas peticiones en paralelo
    forkJoin({
      asesores: this.http.get<any>(`${this.baseUrl}/ventas/api/metas/ranking-asesores/?${params.toString()}`).pipe(
        catchError(error => {
          console.error('Error al cargar ranking de asesores:', error);
          return of({ ranking: [] });
        })
      ),
      coordinadores: this.http.get<any>(`${this.baseUrl}/ventas/api/metas/ranking-coordinadores/?${params.toString()}`).pipe(
        catchError(error => {
          console.error('Error al cargar ranking de coordinadores:', error);
          return of({ ranking: [] });
        })
      )
    }).pipe(
      finalize(() => {
        this.loading = false;
      })
    ).subscribe({
      next: (results) => {
        console.log('Resultados de ambas peticiones:', results);

        // Procesar datos de asesores
        if (results.asesores && results.asesores.ranking && Array.isArray(results.asesores.ranking)) {
          console.log('Datos de ranking de asesores:', results.asesores.ranking);

          // Destruir gráfico existente si lo hay
          if (this.chartAsesores) {
            this.chartAsesores.destroy();
          }

          // Renderizar el gráfico de asesores
          this.renderizarGraficoAsesoresRanking(results.asesores.ranking);
        } else {
          console.error('Estructura de datos no reconocida para asesores:', results.asesores);
        }

        // Procesar datos de coordinadores
        if (results.coordinadores && results.coordinadores.ranking && Array.isArray(results.coordinadores.ranking)) {
          console.log('Datos de ranking de coordinadores:', results.coordinadores.ranking);

          // Destruir gráfico existente si lo hay
          if (this.chartCoordinadores) {
            this.chartCoordinadores.destroy();
          }

          // Renderizar el gráfico de coordinadores
          this.renderizarGraficoCoordinadoresRanking(results.coordinadores.ranking);
        } else {
          console.error('Estructura de datos no reconocida para coordinadores:', results.coordinadores);
        }
      },
      error: (error) => {
        console.error('Error al cargar los datos:', error);
        Swal.fire('Error', 'No se pudieron cargar los datos de los gráficos', 'error');
        this.loading = false;
      }
    });
  }

  // Método para aplicar los filtros
  aplicarFiltros(): void {
    this.cargarDatosGrafico();
  }

  // Método para resetear los filtros
  resetearFiltros(): void {
    const today = new Date();

    // Establecer fecha de inicio como el primer día del mes actual
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    this.filterForm.reset({
      fechaInicio: firstDayOfMonth,
      fechaFin: today
    });

    this.cargarDatosGrafico();
  }

  // Los métodos renderizarGrafico y renderizarGraficoEstados han sido eliminados
  // ya que solo estamos usando los gráficos de asesores y coordinadores

  // Método para renderizar el gráfico de Asesores
  renderizarGraficoAsesores(): void {
    try {
      // Procesar datos para contar ventas por asesor
      const asesoresCount: { [key: string]: number } = {};

      console.log('Procesando datos para gráfico de Asesores');

      this.chartData.forEach(venta => {
        // Solo contar ventas con estado "VALIDADA"
        if (venta.Estado === "VALIDADA") {
          // Combinar nombre y apellido para formar el nombre completo del asesor
          const nombreAsesor = venta.nombre && venta.apellido
            ? `${venta.nombre} ${venta.apellido}`
            : (venta.nombre || venta.apellido || 'Sin Asesor');

          asesoresCount[nombreAsesor] = (asesoresCount[nombreAsesor] || 0) + 1;
        }
      });

      console.log('Asesores contados:', asesoresCount);

      // Ordenar por cantidad de ventas (de mayor a menor)
      const sortedAsesores = Object.entries(asesoresCount)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 8); // Limitar a los 8 principales asesores para mejor visualización

      console.log('Asesores ordenados:', sortedAsesores);

      // Preparar datos para el gráfico
      const labels = sortedAsesores.map(item => item[0]);
      const data = sortedAsesores.map(item => item[1]);

      console.log('Labels para gráfico de Asesores:', labels);
      console.log('Datos para gráfico de Asesores:', data);

      if (labels.length === 0 || data.length === 0) {
        console.warn('No hay datos para mostrar en el gráfico de Asesores');
        return;
      }

      // Generar un gradiente de colores azules para mejor visualización
      const backgroundColors = labels.map((_, index) => {
        const intensity = 55 + (index * 15);
        return `rgba(54, 162, ${intensity}, 0.8)`;
      });

      // Verificar si el elemento canvas existe
      if (!this.chartAsesoresRef || !this.chartAsesoresRef.nativeElement) {
        console.error('Elemento canvas para Asesores no encontrado');
        return;
      }

      // Crear el gráfico
      const ctx = this.chartAsesoresRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('No se pudo obtener el contexto 2D del canvas para Asesores');
        return;
      }

      console.log('Creando gráfico de Asesores');

      this.chartAsesores = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Ventas',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
            borderWidth: 1,
            borderRadius: 4,
            maxBarThickness: 30
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `Ventas: ${context.raw as number}`;
                }
              }
            }
          },
          scales: {
            x: {
              beginAtZero: true,
              grid: {
                display: true,
                drawBorder: true,
                color: 'rgba(200, 200, 200, 0.3)'
              },
              ticks: {
                precision: 0
              }
            },
            y: {
              grid: {
                display: false
              }
            }
          }
        }
      });

      console.log('Gráfico de Asesores creado correctamente');
    } catch (error) {
      console.error('Error al renderizar el gráfico de Asesores:', error);
    }
  }

  // Método para renderizar el gráfico de Coordinadores
  renderizarGraficoCoordinadores(): void {
    try {
      // Procesar datos para contar ventas por coordinador
      const coordinadoresCount: { [key: string]: number } = {};

      console.log('Procesando datos para gráfico de Coordinadores');

      this.chartData.forEach(venta => {
        // Solo contar ventas con estado "VALIDADA"
        if (venta.Estado === "VALIDADA") {
          const coordinador = venta.Coordinador || 'Sin Coordinador';
          coordinadoresCount[coordinador] = (coordinadoresCount[coordinador] || 0) + 1;
        }
      });

      console.log('Coordinadores contados:', coordinadoresCount);

      // Ordenar por cantidad de ventas (de mayor a menor)
      const sortedCoordinadores = Object.entries(coordinadoresCount)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 8); // Limitar a los 8 principales coordinadores para mejor visualización

      console.log('Coordinadores ordenados:', sortedCoordinadores);

      // Preparar datos para el gráfico
      const labels = sortedCoordinadores.map(item => item[0]);
      const data = sortedCoordinadores.map(item => item[1]);

      console.log('Labels para gráfico de Coordinadores:', labels);
      console.log('Datos para gráfico de Coordinadores:', data);

      if (labels.length === 0 || data.length === 0) {
        console.warn('No hay datos para mostrar en el gráfico de Coordinadores');
        return;
      }

      // Generar un gradiente de colores verdes para mejor visualización
      const backgroundColors = labels.map((_, index) => {
        const intensity = 55 + (index * 15);
        return `rgba(75, ${intensity}, 192, 0.8)`;
      });

      // Verificar si el elemento canvas existe
      if (!this.chartCoordinadoresRef || !this.chartCoordinadoresRef.nativeElement) {
        console.error('Elemento canvas para Coordinadores no encontrado');
        return;
      }

      // Crear el gráfico
      const ctx = this.chartCoordinadoresRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('No se pudo obtener el contexto 2D del canvas para Coordinadores');
        return;
      }

      console.log('Creando gráfico de Coordinadores');

      this.chartCoordinadores = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Ventas',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
            borderWidth: 1,
            borderRadius: 4,
            maxBarThickness: 30
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `Ventas: ${context.raw as number}`;
                }
              }
            }
          },
          scales: {
            x: {
              beginAtZero: true,
              grid: {
                display: true,
                drawBorder: true,
                color: 'rgba(200, 200, 200, 0.3)'
              },
              ticks: {
                precision: 0
              }
            },
            y: {
              grid: {
                display: false
              }
            }
          }
        }
      });

      console.log('Gráfico de Coordinadores creado correctamente');
    } catch (error) {
      console.error('Error al renderizar el gráfico de Coordinadores:', error);
    }
  }

  // Método para renderizar el gráfico de Asesores con datos de ranking
  renderizarGraficoAsesoresRanking(rankingData: any[]): void {
    try {
      console.log('Procesando datos de ranking para gráfico de Asesores');

      if (!rankingData || rankingData.length === 0) {
        console.warn('No hay datos de ranking para mostrar en el gráfico de Asesores');
        return;
      }

      // Preparar datos para el gráfico
      const labels = rankingData.map(item => item.nombre_completo || `${item.nombre} ${item.apellido}`);
      const data = rankingData.map(item => item.puntos);

      console.log('Labels para gráfico de Asesores:', labels);
      console.log('Datos para gráfico de Asesores:', data);

      // Verificar si el elemento canvas existe
      if (!this.chartAsesoresRef || !this.chartAsesoresRef.nativeElement) {
        console.error('Elemento canvas para Asesores no encontrado');
        console.log('Intentando de nuevo en 500ms...');

        // Intentar de nuevo después de un breve retraso
        setTimeout(() => {
          if (this.chartAsesoresRef && this.chartAsesoresRef.nativeElement) {
            console.log('Elemento canvas para Asesores encontrado en el segundo intento');
            this.renderizarGraficoAsesoresRanking(rankingData);
          } else {
            console.error('Elemento canvas para Asesores no encontrado después del segundo intento');
          }
        }, 500);

        return;
      }

      // Destruir gráfico existente si lo hay
      if (this.chartAsesores) {
        this.chartAsesores.destroy();
      }

      // Generar un gradiente de colores azules para mejor visualización
      const backgroundColors = labels.map((_, index) => {
        const intensity = 55 + (index * 15);
        return `rgba(54, 162, ${intensity}, 0.8)`;
      });

      // Crear el gráfico
      const ctx = this.chartAsesoresRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('No se pudo obtener el contexto 2D del canvas para Asesores');
        return;
      }

      console.log('Creando gráfico de Asesores con datos de ranking');

      this.chartAsesores = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Puntos',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
            borderWidth: 1,
            borderRadius: 4,
            maxBarThickness: 30
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const index = context.dataIndex;
                  const item = rankingData[index];
                  return [
                    `Puntos: ${item.puntos}`,
                    `Ventas totales: ${item.total_ventas}`,
                    `Completas: ${item.ventas_completas || 0}`,
                    `Parciales: ${item.ventas_parciales || 0}`,
                    `Validadas: ${item.ventas_validadas || 0}`
                  ];
                }
              }
            },
            title: {
              display: true,
              text: 'Top Asesores por Puntos'
            }
          },
          scales: {
            x: {
              beginAtZero: true,
              grid: {
                display: true,
                drawBorder: true,
                color: 'rgba(200, 200, 200, 0.3)'
              },
              ticks: {
                precision: 0
              },
              title: {
                display: true,
                text: 'Puntos'
              }
            },
            y: {
              grid: {
                display: false
              }
            }
          }
        }
      });

      console.log('Gráfico de Asesores con ranking creado correctamente');
    } catch (error) {
      console.error('Error al renderizar el gráfico de Asesores con ranking:', error);
    }
  }

  // Método para renderizar el gráfico de Coordinadores con datos de ranking
  renderizarGraficoCoordinadoresRanking(rankingData: any[]): void {
    try {
      console.log('Procesando datos de ranking para gráfico de Coordinadores');

      if (!rankingData || rankingData.length === 0) {
        console.warn('No hay datos de ranking para mostrar en el gráfico de Coordinadores');
        return;
      }

      // Preparar datos para el gráfico
      const labels = rankingData.map(item => item.nombre_completo || `${item.nombre} ${item.apellido}`);
      const data = rankingData.map(item => item.puntos);

      console.log('Labels para gráfico de Coordinadores:', labels);
      console.log('Datos para gráfico de Coordinadores:', data);

      // Verificar si el elemento canvas existe
      if (!this.chartCoordinadoresRef || !this.chartCoordinadoresRef.nativeElement) {
        console.error('Elemento canvas para Coordinadores no encontrado');
        console.log('Intentando de nuevo en 500ms...');

        // Intentar de nuevo después de un breve retraso
        setTimeout(() => {
          if (this.chartCoordinadoresRef && this.chartCoordinadoresRef.nativeElement) {
            console.log('Elemento canvas para Coordinadores encontrado en el segundo intento');
            this.renderizarGraficoCoordinadoresRanking(rankingData);
          } else {
            console.error('Elemento canvas para Coordinadores no encontrado después del segundo intento');
          }
        }, 500);

        return;
      }

      // Destruir gráfico existente si lo hay
      if (this.chartCoordinadores) {
        this.chartCoordinadores.destroy();
      }

      // Generar un gradiente de colores verdes para mejor visualización
      const backgroundColors = labels.map((_, index) => {
        const intensity = 55 + (index * 15);
        return `rgba(75, ${intensity}, 192, 0.8)`;
      });

      // Crear el gráfico
      const ctx = this.chartCoordinadoresRef.nativeElement.getContext('2d');
      if (!ctx) {
        console.error('No se pudo obtener el contexto 2D del canvas para Coordinadores');
        return;
      }

      console.log('Creando gráfico de Coordinadores con datos de ranking');

      this.chartCoordinadores = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Puntos',
            data: data,
            backgroundColor: backgroundColors,
            borderColor: backgroundColors.map(color => color.replace('0.8', '1')),
            borderWidth: 1,
            borderRadius: 4,
            maxBarThickness: 30
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const index = context.dataIndex;
                  const item = rankingData[index];
                  return [
                    `Puntos: ${item.puntos}`,
                    `Ventas totales: ${item.total_ventas}`,
                    `Completas: ${item.ventas_completas || 0}`,
                    `Parciales: ${item.ventas_parciales || 0}`,
                    `Validadas: ${item.ventas_validadas || 0}`,
                    `Asesores activos: ${item.asesores_activos || 0} / ${item.total_asesores || 0}`
                  ];
                }
              }
            },
            title: {
              display: true,
              text: 'Top Coordinadores por Puntos'
            }
          },
          scales: {
            x: {
              beginAtZero: true,
              grid: {
                display: true,
                drawBorder: true,
                color: 'rgba(200, 200, 200, 0.3)'
              },
              ticks: {
                precision: 0
              },
              title: {
                display: true,
                text: 'Puntos'
              }
            },
            y: {
              grid: {
                display: false
              }
            }
          }
        }
      });

      console.log('Gráfico de Coordinadores con ranking creado correctamente');
    } catch (error) {
      console.error('Error al renderizar el gráfico de Coordinadores con ranking:', error);
    }
  }

  // Método para generar colores aleatorios
  generateRandomColors(count: number): string[] {
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      const hue = (i * 137) % 360; // Distribución uniforme de colores
      colors.push(`hsl(${hue}, 70%, 60%)`);
    }
    return colors;
  }

  // Métodos para manejar el modal de asesores
  abrirModalMetas(): void {
    this.mostrarModalAsesores = true;
    this.cargarAsesores();
  }

  cerrarModalAsesores(): void {
    this.mostrarModalAsesores = false;
    this.asesores = [];
    this.asesoresFiltrados = [];
    this.terminoBusqueda = '';
    this.paginaActual = 1;
  }

  // Método para cargar la lista de asesores
  cargarAsesores(): void {
    this.loadingAsesores = true;
    this.paginaActual = 1;
    this.terminoBusqueda = '';

    this.http.get<any[]>(`${this.baseUrl}/ventas/api/asesores/listado/`).pipe(
      catchError(error => {
        console.error('Error al cargar asesores:', error);
        Swal.fire('Error', 'No se pudieron cargar los asesores', 'error');
        return of([]);
      }),
      finalize(() => {
        this.loadingAsesores = false;
      })
    ).subscribe(data => {
      if (data && Array.isArray(data)) {
        this.asesores = data;
        console.log('Asesores cargados:', this.asesores);
        // Aplicar filtros iniciales y paginación
        this.filtrarAsesores();
      } else {
        console.error('Los datos recibidos no son un array:', data);
        this.asesores = [];
        this.asesoresFiltrados = [];
        this.totalPaginas = 1;
      }
    });
  }

  // Métodos para manejar el modal de metas
  verMetasAsesor(asesor: any): void {
    this.asesorSeleccionado = asesor;
    this.mostrarModalMetas = true;
    this.cargarMetasAsesor(asesor.codi_usuario);
  }

  cerrarModalMetas(): void {
    this.mostrarModalMetas = false;
    this.metas = [];
  }

  // Método para cargar las metas de un asesor
  cargarMetasAsesor(idUsuario: number): void {
    this.loadingMetas = true;

    this.http.get<any[]>(`${this.baseUrl}/ventas/api/metas/usuario/${idUsuario}/`).pipe(
      catchError(error => {
        console.error('Error al cargar metas:', error);
        Swal.fire('Error', 'No se pudieron cargar las metas del asesor', 'error');
        return of([]);
      }),
      finalize(() => {
        this.loadingMetas = false;
      })
    ).subscribe(data => {
      if (data && Array.isArray(data)) {
        this.metas = data;
        console.log('Metas cargadas:', this.metas);
      } else {
        console.error('Los datos recibidos no son un array:', data);
        this.metas = [];
      }
    });
  }

  // Métodos para manejar el modal de registro de meta
  registrarMetaAsesor(asesor: any): void {
    this.asesorSeleccionado = asesor;

    // Verificar si el asesor ya tiene una meta activa
    this.verificarMetasActivas(asesor.codi_usuario);
  }

  // Método para verificar si el asesor ya tiene metas activas
  verificarMetasActivas(idUsuario: number): void {
    this.loadingMetas = true;

    this.http.get<any>(`${this.baseUrl}/ventas/api/metas/usuario/${idUsuario}/`).pipe(
      catchError(error => {
        console.error('Error al verificar metas:', error);
        Swal.fire('Error', 'No se pudieron verificar las metas del asesor', 'error');
        return of({});
      }),
      finalize(() => {
        this.loadingMetas = false;
      })
    ).subscribe(response => {
      console.log('Respuesta de verificación de metas:', response);

      let metasData: any[] = [];

      // Verificar si la respuesta tiene una propiedad 'data' que contiene las metas
      if (response && response.data && Array.isArray(response.data)) {
        metasData = response.data;
      }
      // Si la respuesta es directamente un array
      else if (response && Array.isArray(response)) {
        metasData = response;
      }

      // Verificar si hay metas activas (donde la fecha de fin es posterior a hoy)
      const today = new Date();
      const metasActivas = metasData.filter(meta => {
        const fechaFin = new Date(meta.fecha_fin);
        return fechaFin >= today;
      });

      if (metasActivas.length > 0) {
        // Si hay metas activas, mostrar advertencia
        Swal.fire({
          title: 'Meta Existente',
          text: `El asesor ${this.asesorSeleccionado.nombre} ${this.asesorSeleccionado.apellido} ya tiene ${metasActivas.length} meta(s) activa(s). ¿Desea registrar otra meta?`,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Sí, registrar',
          cancelButtonText: 'No, cancelar'
        }).then((result) => {
          if (result.isConfirmed) {
            this.abrirModalRegistroMeta();
          }
        });
      } else {
        // Si no hay metas activas, abrir el modal directamente
        this.abrirModalRegistroMeta();
      }
    });
  }

  // Método para abrir el modal de registro de meta
  abrirModalRegistroMeta(): void {
    this.mostrarModalRegistroMeta = true;

    // Resetear el formulario
    this.metaForm.reset();

    // Establecer fechas por defecto (inicio: hoy, fin: fin de mes)
    const today = new Date();
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    this.metaForm.patchValue({
      fechaInicio: this.formatDate(today),
      fechaFin: this.formatDate(endOfMonth)
    });
  }

  cerrarModalRegistroMeta(): void {
    this.mostrarModalRegistroMeta = false;
  }

  // Método para guardar una nueva meta
  guardarMeta(): void {
    if (this.metaForm.invalid) {
      return;
    }

    this.guardandoMeta = true;
    const formValues = this.metaForm.value;

    const metaData = {
      idUsuario: this.asesorSeleccionado.codi_usuario,
      fecha_inicio: formValues.fechaInicio,
      fecha_fin: formValues.fechaFin,
      meta: formValues.cantidadMeta,
      resultado: 0 // Inicialmente en 0
    };

    this.http.post(`${this.baseUrl}/ventas/api/metas/`, metaData).pipe(
      catchError(error => {
        console.error('Error al guardar meta:', error);
        Swal.fire('Error', 'No se pudo guardar la meta', 'error');
        return of(null);
      }),
      finalize(() => {
        this.guardandoMeta = false;
      })
    ).subscribe(response => {
      if (response) {
        Swal.fire('Éxito', 'Meta registrada correctamente', 'success');
        this.cerrarModalRegistroMeta();

        // Si el modal de metas está abierto, actualizar la lista
        if (this.mostrarModalMetas) {
          this.cargarMetasAsesor(this.asesorSeleccionado.codi_usuario);
        }
      }
    });
  }

  // Método para filtrar asesores según el término de búsqueda
  filtrarAsesores(): void {
    // Si no hay término de búsqueda, mostrar todos los asesores
    if (!this.terminoBusqueda.trim()) {
      this.asesoresFiltrados = [...this.asesores];
    } else {
      // Filtrar por nombre, apellido, DNI o ciudad
      const termino = this.terminoBusqueda.toLowerCase().trim();
      this.asesoresFiltrados = this.asesores.filter(asesor =>
        (asesor.nombre && asesor.nombre.toLowerCase().includes(termino)) ||
        (asesor.apellido && asesor.apellido.toLowerCase().includes(termino)) ||
        (asesor.dni && asesor.dni.toLowerCase().includes(termino)) ||
        (asesor.ciudad && asesor.ciudad.toLowerCase().includes(termino))
      );
    }

    // Calcular el total de páginas
    this.totalPaginas = Math.ceil(this.asesoresFiltrados.length / this.elementosPorPagina);

    // Asegurarse de que la página actual es válida
    if (this.paginaActual > this.totalPaginas) {
      this.paginaActual = Math.max(1, this.totalPaginas);
    }
  }

  // Método para buscar asesores
  buscarAsesores(evento: Event): void {
    const input = evento.target as HTMLInputElement;
    this.terminoBusqueda = input.value;
    this.paginaActual = 1; // Volver a la primera página al buscar
    this.filtrarAsesores();
  }

  // Método para cambiar de página
  cambiarPagina(pagina: number): void {
    if (pagina >= 1 && pagina <= this.totalPaginas) {
      this.paginaActual = pagina;
    }
  }

  // Método para obtener los asesores de la página actual
  get asesoresPaginados(): any[] {
    const inicio = (this.paginaActual - 1) * this.elementosPorPagina;
    const fin = inicio + this.elementosPorPagina;
    return this.asesoresFiltrados.slice(inicio, fin);
  }

  // Método para generar el array de páginas para la paginación
  get paginas(): number[] {
    const paginas: number[] = [];
    const maxPaginasMostradas = 5;

    if (this.totalPaginas <= maxPaginasMostradas) {
      // Si hay pocas páginas, mostrar todas
      for (let i = 1; i <= this.totalPaginas; i++) {
        paginas.push(i);
      }
    } else {
      // Si hay muchas páginas, mostrar un subconjunto centrado en la página actual
      let inicio = Math.max(1, this.paginaActual - Math.floor(maxPaginasMostradas / 2));
      let fin = inicio + maxPaginasMostradas - 1;

      if (fin > this.totalPaginas) {
        fin = this.totalPaginas;
        inicio = Math.max(1, fin - maxPaginasMostradas + 1);
      }

      for (let i = inicio; i <= fin; i++) {
        paginas.push(i);
      }
    }

    return paginas;
  }

  // Método para verificar si una meta está activa (fecha fin posterior a hoy)
  esMetaActiva(meta: any): boolean {
    if (!meta || !meta.fecha_fin) {
      return false;
    }

    const fechaFin = new Date(meta.fecha_fin);
    const today = new Date();

    // Establecer las horas, minutos, segundos y milisegundos a 0 para comparar solo las fechas
    today.setHours(0, 0, 0, 0);

    return fechaFin >= today;
  }

  // Método para eliminar una meta
  eliminarMeta(meta: any): void {
    if (!meta || !meta.id) {
      Swal.fire('Error', 'No se pudo identificar la meta a eliminar', 'error');
      return;
    }

    // Mostrar confirmación antes de eliminar
    Swal.fire({
      title: '¿Eliminar meta?',
      text: `¿Está seguro que desea eliminar la meta con ID ${meta.id}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar',
      confirmButtonColor: '#d33',
    }).then((result) => {
      if (result.isConfirmed) {
        // Proceder con la eliminación
        this.http.delete(`${this.baseUrl}/ventas/api/metas/${meta.id}/`).pipe(
          catchError(error => {
            console.error('Error al eliminar meta:', error);
            Swal.fire('Error', 'No se pudo eliminar la meta', 'error');
            return of(null);
          })
        ).subscribe(response => {
          console.log('Respuesta de eliminación:', response);

          // Mostrar mensaje de éxito
          Swal.fire('Eliminada', 'La meta ha sido eliminada correctamente', 'success');

          // Actualizar la lista de metas
          this.cargarMetasAsesor(this.asesorSeleccionado.codi_usuario);
        });
      }
    });
  }

  // Método auxiliar para formatear fechas para inputs de tipo date
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}


