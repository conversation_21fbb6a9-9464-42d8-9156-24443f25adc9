<section class=" w-full flex flex-col items-center p-5">
    <form [formGroup]="ventaForm" (ngSubmit)="onSubmit()" class="w-full max-w-[80%]">
        <!-- Selector de tipo de cliente -->
        <div class="tipo-cliente-selector">
            <div
                class=" w-full flex justify-start items-center    bg-white dark:bg-gray-800 rounded-md shadow-sm  p-2 mb-4">
                <mat-card-content>
                    <div class="radio-group-container">
                        <label class="radio-group-label">Segmento:</label>
                        <mat-radio-group [(ngModel)]="tipoCliente" (change)="onTipoClienteChange($event)"
                            [ngModelOptions]="{standalone: true}" class="radio-group">
                            <mat-radio-button value="persona" color="primary" [checked]="true">
                                <div class="radio-content">
                                    <mat-icon>person</mat-icon>
                                    <span>Persona</span>
                                </div>
                            </mat-radio-button>
                            <mat-radio-button value="empresa" color="primary">
                                <div class="radio-content">
                                    <mat-icon>business</mat-icon>
                                    <span>Empresa</span>
                                </div>
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </mat-card-content>
            </div>
        </div>

        <!-- TARJETA 1: DATOS DEL CLIENTE -->
        <mat-card class="main-card datos-cliente">
            <div class="icon-container">
                <mat-icon class="icono-flotante">account_circle</mat-icon>
            </div>
            <mat-card-content>
                <section class="datos-cliente-content">
                    <div class="header-row">
                        <h2 class="section-title">{{ tipoCliente === 'persona' ? 'Datos del Cliente Residencial' :
                            'Datos de la Empresa' }}</h2>
                    </div>
                    <hr class="title-separator" />

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                        <!-- Campos existentes -->
                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Campaña</label>
                            <select formControlName="campania"
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                <option *ngFor="let operador of campanias" [value]="operador">
                                    {{ operador }}
                                </option>
                            </select>
                        </div>

                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{
                                tipoCliente === 'persona' ? 'Nombres y Apellidos' : 'Nombre de la Empresa' }}</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="nombres_apellidos" required />
                            <mat-error *ngIf="ventaForm.get('email')?.errors?.['required']"
                                class="!text-xs text-red-600 ">El Nombre es requerido</mat-error>

                        </div>

                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ tipoCliente === 'empresa' ? 'CIF' : 'NIF / NIE' }}</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="nif_nie" required maxlength="9" />
                            <mat-error *ngIf="ventaForm.get('email')?.errors?.['required']"
                                class="!text-xs text-red-600 ">El documento es requerido</mat-error>
                            <mat-error *ngIf="ventaForm.get('email')?.errors?.['pattern']"
                                class="!text-xs text-red-600 ">El documento debe tener 8 dígitos y una letra</mat-error>
                        </div>

                        <!-- Campos adicionales para empresa que aparecen solo cuando tipoCliente es 'empresa' -->
                        <ng-container *ngIf="tipoCliente === 'empresa'">
                            <div class="flex flex-col w-full gap-2">
                                <label
                                    class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nombres
                                    y Apellidos de Representante Legal</label>
                                <input
                                    class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                    formControlName="nombresApellidosRL" />
                            </div>

                            <div class="flex flex-col w-full gap-2">
                                <label
                                    class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">NIF/NIE
                                    Representante Legal</label>
                                <input
                                    class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                    formControlName="nif_nieRL" />
                            </div>
                        </ng-container>

                        <!-- Resto de los campos existentes -->
                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nacionalidad</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="nacionalidad" />
                        </div>

                        <!-- Fecha de nacimiento -->
                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha
                                de nacimiento</label>
                            <div
                                class="flex w-full border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                <input class="w-full outline-none border-none indent-5"
                                    [matDatepicker]="pickerNacimiento" formControlName="nacimiento" />
                                <mat-datepicker-toggle matSuffix [for]="pickerNacimiento"
                                    class="max-w-[2rem]  "></mat-datepicker-toggle>
                                <mat-datepicker #pickerNacimiento class="w-50"></mat-datepicker>
                            </div>
                        </div>

                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Genero
                            </label>
                            <select formControlName="genero"
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                <option value="Masculino">Masculino</option>
                                <option value="Femenino">Femenino</option>
                            </select>
                        </div>


                        <!-- Nueva fila para Cuenta Bancaria y Correo Electrónico -->
                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cuenta
                                Bancaria</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="cuenta_bancaria" />
                        </div>

                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Correo
                                Electrónico *</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="email" required />
                            <mat-error *ngIf="ventaForm.get('email')?.errors?.['required']"
                                class="!text-xs text-red-600 ">El correo electrónico es obligatorio</mat-error>
                            <mat-error *ngIf="ventaForm.get('email')?.errors?.['email']"
                                class="!text-xs text-red-600 ">El correo electrónico no es válido</mat-error>
                        </div>
                        <div class="flex flex-col w-full gap-2" *ngIf="isCoordinador()">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Usuario
                                RetailX</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="usuarioRetailX" />
                        </div>
                        <div class="flex flex-col w-full gap-2" *ngIf="isCoordinador()">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Usuario
                                Solivesa</label>
                            <input
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                formControlName="usuarioSolivesa" />
                        </div>
                        <div class="flex flex-col w-full gap-2">
                            <label
                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Permanencia
                            </label>
                            <select formControlName="permanencia"
                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                <option value="Si">Si</option>
                                <option value="No">No</option>
                            </select>
                        </div>


                    </div>
                </section>
            </mat-card-content>
        </mat-card>

        <!-- DIRECCIONES Y PROMOCIONES -->
        <div formArrayName="direccionesPromociones">
            <div *ngFor="let direccionPromocion of direccionesPromociones.controls; let i = index">
                <div [formGroupName]="i" class="direccion-promocion-container">
                    <!-- Dirección -->
                    <mat-card class="main-card direccion">
                        <div class="icon-container">
                            <mat-icon class="icono-flotante">location_on</mat-icon>
                        </div>
                        <mat-card-content>
                            <div class="header-row">
                                <h2 class="section-title">Dirección</h2>
                                <button type="button" mat-icon-button color="warn"
                                    (click)="eliminarDireccionPromocion(i)" *ngIf="direccionesPromociones.length > 1">
                                    <mat-icon>delete</mat-icon>
                                </button>
                            </div>
                            <div class="grid-single-column">

                                <!-- Dirección - Layout compacto y profesional -->
                                <div class="grid grid-cols-12 gap-4 mb-4">
                                    <!-- Provincia (Catastro) -->
                                    <div class="col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Provincia</label>
                                        <div class="relative provincia-dropdown">
                                            <input
                                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
                                                name="provinciaFilter_{{i}}"
                                                [(ngModel)]="direccionesData[i].provincia"
                                                placeholder="Escriba o seleccione una provincia"
                                                autocomplete="new-password" autocorrect="off"
                                                autocapitalize="off" spellcheck="false"
                                                (click)="toggleProvinciasDropdown($event)"
                                                (input)="filtrarProvincias($event); actualizarProvinciaManual(i, $event)"
                                                (blur)="actualizarDireccionCompleta(i)"
                                                />
                                            <div *ngIf="cargandoProvincias"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                                <mat-spinner diameter="16"></mat-spinner>
                                            </div>

                                            <!-- Dropdown de provincias -->
                                            <div *ngIf="provinciasCatastroFiltradas && provinciasCatastroFiltradas.length > 0 && mostrarProvincias"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-40 overflow-y-auto provincia-dropdown"
                                                (click)="$event.stopPropagation()">
                                                <div *ngFor="let provincia of provinciasCatastroFiltradas"
                                                    class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                                                    (click)="
                        seleccionarProvinciaCatastro(provincia, i);
                        $event.stopPropagation()
                      ">
                                                    {{ provincia.Denominacion }}
                                                </div>
                                                <div *ngIf="provinciasCatastroFiltradas && provinciasCatastroFiltradas.length === 0"
                                                    class="px-3 py-1.5 text-sm text-gray-500 dark:text-gray-400">
                                                    No se encontraron resultados
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <!-- Municipio (Catastro) -->
                                    <div class="col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Municipio</label>
                                        <div class="relative municipio-dropdown">
                                            <input
                                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
                                                name="municipioFilter_{{i}}"
                                                [(ngModel)]="direccionesData[i].distrito"
                                                placeholder="Escriba o seleccione un municipio"
                                                autocomplete="new-password" autocorrect="off"
                                                autocapitalize="off" spellcheck="false"
                                                [disabled]="!direccionesData[i].provincia"
                                                (click)="toggleMunicipiosDropdown($event)"
                                                (input)="filtrarMunicipios($event); actualizarMunicipioManual(i, $event)"
                                                (blur)="actualizarDireccionCompleta(i)" />
                                            <div *ngIf="cargandoMunicipios"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                                <mat-spinner diameter="16"></mat-spinner>
                                            </div>

                                            <!-- Dropdown de municipios -->
                                            <div *ngIf="municipiosCatastroFiltrados && municipiosCatastroFiltrados.length > 0 && mostrarMunicipios"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-40 overflow-y-auto municipio-dropdown"
                                                (click)="$event.stopPropagation()">
                                                <div *ngFor="let municipio of municipiosCatastroFiltrados"
                                                    class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                                                    (click)="
                        seleccionarMunicipioCatastro(municipio, i);
                        $event.stopPropagation()
                      ">
                                                    {{ municipio.Denominacion }}
                                                </div>
                                                <div *ngIf="municipiosCatastroFiltrados && municipiosCatastroFiltrados.length === 0"
                                                    class="px-3 py-1.5 text-sm text-gray-500 dark:text-gray-400">
                                                    No se encontraron resultados
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Vía (Catastro) en una nueva fila -->
                                <div class="grid grid-cols-12 gap-4 mb-4">
                                    <!-- Vía (Catastro) -->
                                    <div class="col-span-12">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Vía</label>
                                        <div class="relative via-dropdown">
                                            <input
                                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white text-sm"
                                                name="viaFilter_{{i}}"
                                                [(ngModel)]="direccionesData[i].via"
                                                placeholder="Escriba o seleccione una vía"
                                                autocomplete="new-password" autocorrect="off"
                                                autocapitalize="off" spellcheck="false"
                                                [disabled]="!direccionesData[i].distrito"
                                                (click)="toggleViasDropdown($event)"
                                                (input)="filtrarVias($event); actualizarViaManual(i, $event)"
                                                (blur)="actualizarDireccionCompleta(i)" />
                                            <div *ngIf="cargandoVias"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                                <mat-spinner diameter="16"></mat-spinner>
                                            </div>

                                            <!-- Dropdown de vías -->
                                            <div *ngIf="viasCatastroFiltradas && viasCatastroFiltradas.length > 0 && mostrarVias"
                                                class="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-40 overflow-y-auto via-dropdown"
                                                (click)="$event.stopPropagation()">
                                                <div *ngFor="let via of viasCatastroFiltradas"
                                                    class="px-3 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                                                    (click)="
                        seleccionarViaCatastro(via, i); $event.stopPropagation()
                      ">
                                                    {{ via.DenominacionCompleta }}
                                                </div>
                                                <div *ngIf="viasCatastroFiltradas && viasCatastroFiltradas.length === 0"
                                                    class="px-3 py-1.5 text-sm text-gray-500 dark:text-gray-400">
                                                    No se encontraron resultados
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Detalles de dirección - Layout compacto en tres filas con 2 campos por fila -->
                                <!-- Primera fila de detalles de dirección -->
                                <div class="grid grid-cols-12 gap-3 mb-3">
                                    <!-- Número -->
                                    <div class="col-span-6 sm:col-span-6 md:col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Número</label>
                                        <input
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            type="text" name="numero" [(ngModel)]="direccionesData[i].numero"
                                            (ngModelChange)="actualizarDireccionCompleta(i)" placeholder="Nº"
                                            (input)="onNumeroInput($event, i)" />
                                    </div>

                                    <!-- Bloque -->
                                    <div class="col-span-6 sm:col-span-6 md:col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bloque</label>
                                        <input
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            type="text" name="bloque" [(ngModel)]="direccionesData[i].bloque"
                                            (ngModelChange)="actualizarDireccionCompleta(i)" placeholder="Bloque"
                                            (input)="onBloqueInput($event, i)" />
                                    </div>
                                </div>

                                <!-- Segunda fila de detalles de dirección -->
                                <div class="grid grid-cols-12 gap-3 mb-3">
                                    <!-- Escalera -->
                                    <div class="col-span-6 sm:col-span-6 md:col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Escalera</label>
                                        <input
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            type="text" name="escalera" [(ngModel)]="direccionesData[i].escalera"
                                            (ngModelChange)="actualizarDireccionCompleta(i)" placeholder="Esc."
                                            (input)="onEscaleraInput($event, i)" />
                                    </div>

                                    <!-- Planta -->
                                    <div class="col-span-6 sm:col-span-6 md:col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Planta</label>
                                        <input
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            type="text" name="planta" [(ngModel)]="direccionesData[i].planta"
                                            (ngModelChange)="actualizarDireccionCompleta(i)" placeholder="Planta"
                                            (input)="onPlantaInput($event, i)" />
                                    </div>
                                </div>

                                <!-- Tercera fila de detalles de dirección -->
                                <div class="grid grid-cols-12 gap-3 mb-3">
                                    <!-- Puerta -->
                                    <div class="col-span-6 sm:col-span-6 md:col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Puerta</label>
                                        <input
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            type="text" name="puerta" [(ngModel)]="direccionesData[i].puerta"
                                            (ngModelChange)="actualizarDireccionCompleta(i)" placeholder="Puerta"
                                            (input)="onPuertaInput($event, i)" />
                                    </div>

                                    <!-- Código postal -->
                                    <div class="col-span-6 sm:col-span-6 md:col-span-6">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Codigo
                                            Postal</label>
                                        <input
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            type="text" name="codigoPostal2"
                                            [(ngModel)]="direccionesData[i].codigoPostal" maxlength="5"
                                            pattern="^[0-9]{5}$" (keypress)="validateNumberInput($event)"
                                            (paste)="validatePaste($event)" placeholder="00000"
                                            (input)="onCodigoPostalInput($event, i)"
                                            (ngModelChange)="actualizarDireccionCompleta(i)" />
                                    </div>
                                </div>

                                <!-- Dirección completa en una fila completa -->
                                <div class="grid grid-cols-12 gap-3 mb-3">
                                    <!-- Dirección completa -->
                                    <!-- <div class="col-span-12">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dirección
                                            completa</label>
                                        <textarea
                                            class="w-full px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                            name="direccion" formControlName="direccion" rows="2"
                                            placeholder="La dirección completa se generará automáticamente (si no se completa, escribirá la dirección por defecto)"></textarea>
                                    </div> -->
                                    <div class="col-span-12">
                                        <label
                                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dirección
                                            completa</label>
                                        <div class="relative">
                                            <textarea
                                                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                                                name="direccion" formControlName="direccion" rows="2"
                                                 rows="2"
                                                placeholder="La dirección completa se generará automáticamente"></textarea>

                                            <!-- Botón para ver en mapa (posicionado en la esquina inferior derecha del textarea) -->
                                            <button type="button"
                                                class="absolute bottom-3 right-14 w-auto h-10 px-3 flex items-center justify-center rounded-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white shadow-lg transform transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:from-gray-400 disabled:to-gray-500 dark:disabled:from-gray-700 dark:disabled:to-gray-800"
                                                matTooltip="Generar dirección completa automáticamente"
                                                (click)="actualizarDireccionCompleta(i)"
                                            >
                                                <span class="text-xs font-medium mr-1">Generar dirección</span>
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                            <button type="button"
                                                class="absolute bottom-3 right-2 w-10 h-10 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg transform transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:from-gray-400 disabled:to-gray-500 dark:disabled:from-gray-700 dark:disabled:to-gray-800"
                                                matTooltip="Ver ubicación en mapa (requiere provincia, municipio y vía)"
                                                (click)="verEnMapa(i)"
                                                [disabled]="!direccionesData[i].provincia || !direccionesData[i].distrito || !(selectedViaCatastro || viaFilterControl.value || direccionesData[i].via)">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </mat-card-content>

                    </mat-card>

                    <!-- Datos de la Promoción -->
                    <mat-card class="main-card datos-promocion">
                        <div class="icon-container">
                            <mat-icon class="icono-flotante">local_offer</mat-icon>
                        </div>
                        <mat-card-content>
                            <section class="datos-promocion-content">
                                <div class="header-row">
                                    <h2 class="section-title">Datos de la Promoción</h2>
                                </div>
                                <hr class="title-separator" />

                                <div class="grid-single-column">
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Promoción</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="promocion" />
                                    </div>
                                </div>

                                <div class="grid-single-column" style="margin-top: 1rem;">
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">TV
                                            / Deco</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="tvDeco" />
                                    </div>
                                </div>
                                <div class="grid-triple-column">

                                    <!--TIPO FIBRA -->
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo
                                            de Fibra</label>
                                        <select formControlName="tipoFibra"
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                            <option [value]="''">Ninguno</option>

                                            <option *ngFor="let tipo of tiposFibra" [value]="tipo">
                                                {{ tipo }}
                                            </option>
                                        </select>
                                    </div>

                                    <!--HORA INSTALACION -->
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hora
                                            de Instalación </label>
                                        <select formControlName="horaInstalacion"
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                            <option value="Mañana">Mañana</option>
                                            <option value="Tarde">Tarde</option>
                                            <option value="Noche">Noche</option>
                                        </select>
                                    </div>
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo de Venta </label>
                                        <select formControlName="tipoVenta"
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                            <option value="Televenta">Televenta</option>
                                            <option value="Presencial">Presencial</option>
                                         </select>
                                    </div>
                                </div>
                                <div class="grid-triple-column">
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fijo
                                            de Compañía</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="telefonoCompania" />
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                            contacto</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="movilContacto" />
                                    </div>


                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                            a portar 1</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="movilAPortar1" type="text" maxlength="9" pattern="[0-9]*"
                                            (keypress)="validateNumberInput($event)" (paste)="validatePaste($event)">
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                            a portar 2</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="movilAPortar2" type="text" maxlength="9" pattern="[0-9]*"
                                            (keypress)="validateNumberInput($event)" (paste)="validatePaste($event)">
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                            a portar 3</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="movilAPortar3" type="text" maxlength="9" pattern="[0-9]*"
                                            (keypress)="validateNumberInput($event)" (paste)="validatePaste($event)">
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                            a portar 4</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="movilAPortar4" type="text" maxlength="9" pattern="[0-9]*"
                                            (keypress)="validateNumberInput($event)" (paste)="validatePaste($event)">
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                            a portar 5</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="movilAPortar5" type="text" maxlength="9" pattern="[0-9]*"
                                            (keypress)="validateNumberInput($event)" (paste)="validatePaste($event)">
                                    </div>

                                    <!-- Móviles adicionales (mostrados progresivamente) -->
                                    <ng-container *ngIf="tipoCliente === 'empresa'">
                                        <div class="flex flex-col w-full gap-2" *ngIf="movilesVisibles >= 6">
                                            <label
                                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                                a portar 6</label>
                                            <input
                                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                                formControlName="movilAPortar6" type="text" maxlength="9"
                                                pattern="[0-9]*" (keypress)="validateNumberInput($event)"
                                                (paste)="validatePaste($event)">
                                        </div>

                                        <div class="flex flex-col w-full gap-2" *ngIf="movilesVisibles >= 7">
                                            <label
                                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                                a portar 7</label>
                                            <input
                                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                                formControlName="movilAPortar7" type="text" maxlength="9"
                                                pattern="[0-9]*" (keypress)="validateNumberInput($event)"
                                                (paste)="validatePaste($event)">
                                        </div>

                                        <div class="flex flex-col w-full gap-2" *ngIf="movilesVisibles >= 8">
                                            <label
                                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                                a portar 8</label>
                                            <input
                                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                                formControlName="movilAPortar8" type="text" maxlength="9"
                                                pattern="[0-9]*" (keypress)="validateNumberInput($event)"
                                                (paste)="validatePaste($event)">
                                        </div>

                                        <div class="flex flex-col w-full gap-2" *ngIf="movilesVisibles >= 9">
                                            <label
                                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                                a portar 9</label>
                                            <input
                                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                                formControlName="movilAPortar9" type="text" maxlength="9"
                                                pattern="[0-9]*" (keypress)="validateNumberInput($event)"
                                                (paste)="validatePaste($event)">
                                        </div>

                                        <div class="flex flex-col w-full gap-2" *ngIf="movilesVisibles >= 10">
                                            <label
                                                class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil
                                                a portar 10</label>
                                            <input
                                                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                                formControlName="movilAPortar10" type="text" maxlength="9"
                                                pattern="[0-9]*" (keypress)="validateNumberInput($event)"
                                                (paste)="validatePaste($event)">
                                        </div>

                                        <!-- Botón para agregar más móviles -->
                                        <button *ngIf="movilesVisibles < 10" type="button" mat-stroked-button
                                            color="primary" (click)="mostrarSiguienteMovil()">
                                            <mat-icon>add</mat-icon>
                                            Agregar móvil
                                        </button>
                                    </ng-container>
                                </div>

                                <div class="grid-triple-column">
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Precio
                                            Promoción / Tiempo</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="precioPromocionTiempo" />
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Precio
                                            real</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="precioReal" />
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Presentación
                                            con el cliente</label>
                                        <input
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="presentacionCCliente" />
                                    </div>
                                </div>

                                <div class="grid-doble-column">
                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Operador</label>
                                        <select formControlName="operador"
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                            <option *ngFor="let operador of operadores" [value]="operador">
                                                {{ operador }}
                                            </option>
                                        </select>
                                    </div>

                                    <div class="flex flex-col w-full gap-2">
                                        <label
                                            class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Observación</label>
                                        <textarea
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                                            formControlName="operacion" rows="4"></textarea>
                                    </div>
                                </div>
                            </section>
                        </mat-card-content>
                    </mat-card>
                </div>
            </div>
        </div>

        <!-- Botón para agregar nueva dirección -->
        <div class="add-direccion-button-container">
            <button type="button"
                class="flex items-center justify-center gap-2 bg-yellow-600 hover:bg-yellow-700 dark:bg-yellow-700 dark:hover:bg-yellow-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
                (click)="agregarDireccionPromocion()">
                <mat-icon>add</mat-icon>
                Agregar Nueva Venta
            </button>
        </div>
    </form>
</section>

<!-- Pie o botón de cerrar sesión -->
<div class="w-full flex justify-end gap-3 p-5 max-w-[80%] mx-auto">
    <button type="button"
        class="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition shadow"
        (click)="resetForm()">
        Limpiar
    </button>
    <button type="submit"
        class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition shadow"
        [disabled]="loading || !ventaForm.valid" (click)="onSubmit()">
        <mat-spinner *ngIf="loading" diameter="20" style="display: inline-block; margin-right: 8px;">
        </mat-spinner>
        {{ loading ? 'Guardando ventas...' : 'Guardar' }}
    </button>
</div>
