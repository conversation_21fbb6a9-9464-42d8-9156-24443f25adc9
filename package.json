{"name": "client-inmueble-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "npm run start:dev", "build": "npm run build:dev", "watch": "ng build --watch --configuration development", "test": "ng test", "start:dev": "ng serve --c=dev", "build:dev": "ng build --c=dev", "start:prod": "ng serve --c=prod", "build:prod": "ng build --c=prod"}, "private": true, "dependencies": {"@angular/animations": "^14.1.0", "@angular/cdk": "^14.1.0", "@angular/common": "^14.1.0", "@angular/compiler": "^14.1.0", "@angular/core": "^14.1.0", "@angular/fire": "^7.4.1", "@angular/flex-layout": "^14.0.0-beta.40", "@angular/forms": "^14.1.0", "@angular/localize": "^14.1.0", "@angular/material": "^14.1.0", "@angular/material-moment-adapter": "^14.2.7", "@angular/platform-browser": "^14.1.0", "@angular/platform-browser-dynamic": "^14.1.0", "@angular/router": "^14.1.0", "@angular/service-worker": "^14.1.0", "@fullcalendar/angular": "^6.1.8", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@ng-bootstrap/ng-bootstrap": "^13.1.0", "@ngrx/effects": "^14.0.2", "@ngrx/entity": "^14.0.2", "@ngrx/store": "^14.0.2", "@ngrx/store-devtools": "^14.0.2", "@ngx-translate/core": "^14.0.0", "@popperjs/core": "^2.11.8", "@stomp/stompjs": "^7.1.1", "@swimlane/ngx-charts": "^22.0.0", "@types/leaflet": "^1.9.17", "@types/mapbox-gl": "^3.4.1", "@types/moment": "^2.11.29", "@types/popper.js": "^1.10.1", "buffer": "^6.0.3", "chart.js": "^3.9.1", "firebase": "^9.9.1", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "mapbox-gl": "^3.12.0", "moment": "^2.30.1", "ng2-charts": "^4.1.1", "ngx-bootstrap": "^9.0.0", "ngx-dropzone": "^3.1.0", "ngx-image-cropper": "^6.2.2", "quill": "^1.3.7", "recharts": "^2.15.3", "rxfire": "^6.0.3", "rxjs": "~7.5.0", "sockjs-client": "^1.6.1", "sweetalert2": "^11.20.0", "tslib": "^2.3.0", "uuid": "^8.3.2", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.1.0", "@angular/cli": "~14.1.0", "@angular/compiler-cli": "^14.1.0", "@types/jasmine": "~4.0.0", "@types/sockjs-client": "^1.5.4", "@types/stompjs": "^2.3.9", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.20", "jasmine-core": "~4.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~4.7.2"}}