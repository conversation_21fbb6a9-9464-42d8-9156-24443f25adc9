<div class="w-full px-4 sm:px-6 lg:px-10 xl:px-20 py-6">
  <!-- Contenedor principal -->
  <div
    class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all"
  >
    <!-- Cabecera -->
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
    >
      <div>
        <h2 class="text-xl font-semibold text-blue-600">Lista de Leads</h2>
        <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">
          leads registrados en el sistema
        </p>
      </div>
      <div
        class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
      >
        <button
          type="button"
          (click)="verMetasYEstadisticas()"
          class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          Ver Estadísticas de Asesores
        </button>
        <button
          type="button"
          (click)="downloadExcelByDate()"
          class="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Exportar Reporte por Fecha
        </button>
        <button
          type="button"
          (click)="downloadExcelByDateRange()"
          class="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
          *ngIf="(user$ | async)?.role === 'ADMIN'"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          Exportar Reporte por Rango de Fechas
        </button>
        <button
          type="button"
          (click)="limpiarFiltros()"
          class="flex items-center justify-center gap-2 bg-white hover:bg-gray-50 dark:bg-transparent dark:hover:bg-gray-800 text-red-600 dark:text-red-400 border border-red-600 dark:border-red-400 px-4 py-2 rounded-md font-medium transition shadow"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
          Limpiar
        </button>
        <button
          type="button"
          (click)="aplicarFiltros()"
          class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
            />
          </svg>
          Filtrar
        </button>
      </div>
    </div>

    <!-- Formulario de filtros -->
    <form
      [formGroup]="filterForm"
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
    >
      <div>
        <label
          for="dniAsesor"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >DNI</label
        >
        <input
          id="dniAsesor"
          type="text"
          formControlName="dniAsesor"
          placeholder="DNI"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
        />
      </div>
      <div>
        <label
          for="nombreAsesor"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Nombre</label
        >
        <input
          id="nombreAsesor"
          type="text"
          formControlName="nombreAsesor"
          placeholder="Nombre"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
        />
      </div>
      <div>
        <label
          for="numeroMovil"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Móvil</label
        >
        <input
          id="numeroMovil"
          type="text"
          formControlName="numeroMovil"
          placeholder="Móvil"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
        />
      </div>
      <div>
        <label
          for="fecha"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >Fecha</label
        >
        <input
          id="fecha"
          type="date"
          formControlName="fecha"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
        />
      </div>
    </form>

    <!-- Indicador de carga -->
    <div
      *ngIf="loading$ | async"
      class="flex items-center mx-auto my-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md max-w-md"
    >
      <mat-spinner [diameter]="24" color="primary" class="mr-3"></mat-spinner>
      <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
        Cargando leads...
      </span>
    </div>

    <!-- Tabla -->
    <div class="overflow-x-auto rounded-xl shadow">
      <table
        mat-table
        [dataSource]="tableData"
        class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
      >
        <!-- Columna DNI -->
        <ng-container matColumnDef="dni">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
          >
            DNI
          </th>
          <td
            mat-cell
            *matCellDef="let cliente"
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ cliente.dni }}
          </td>
        </ng-container>

        <!-- Columna Nombre Asesor -->
        <ng-container matColumnDef="asesor">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
          >
            Nombre Asesor
          </th>
          <td
            mat-cell
            *matCellDef="let cliente"
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ cliente.asesor }}
          </td>
        </ng-container>

        <!-- Columna Fecha Ingresado -->
        <ng-container matColumnDef="fechaIngresado">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
          >
            Fecha Ingresado
          </th>
          <td
            mat-cell
            *matCellDef="let cliente"
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            <!-- Si es un array, formatear manualmente -->
            <ng-container
              *ngIf="isArray(cliente.fechaIngresado); else stringDate"
            >
              {{ formatDateArray(cliente.fechaIngresado) }}
            </ng-container>
            <!-- Si es un string, convertir a formato de texto -->
            <ng-template #stringDate>
              {{ formatStringDateToText(cliente.fechaIngresado) }}
            </ng-template>
          </td>
        </ng-container>

        <!-- Columna Número Móvil -->
        <ng-container matColumnDef="numeroMovil">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
          >
            Número Móvil(Cliente)
          </th>
          <td
            mat-cell
            *matCellDef="let cliente"
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ cliente.numeroMovil }}
          </td>
        </ng-container>

        <!-- Columna Coordinador -->
        <ng-container matColumnDef="coordinador">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
          >
            Supervisor
          </th>
          <td
            mat-cell
            *matCellDef="let cliente"
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700"
          >
            {{ cliente.coordinador || "-" }}
          </td>
        </ng-container>

        <!-- Columna Acciones -->
        <ng-container matColumnDef="accion">
          <th
            mat-header-cell
            *matHeaderCellDef
            class="px-6 py-3 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs text-center"
          >
            Acciones
          </th>
          <td
            mat-cell
            *matCellDef="let cliente"
            class="px-6 py-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 text-center"
          >
            <div class="flex justify-start items-center gap-1 mr-auto w-24">
              <button
                mat-icon-button
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition p-1"
                [matTooltip]="'Ver detalles'"
                (click)="onTableEdit(cliente)"
              >
                <mat-icon class="text-current text-lg">visibility</mat-icon>
              </button>
              <!-- Botón Excel -->
              <button
                mat-icon-button
                class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 transition p-1"
                [matTooltip]="'Descargar Excel'"
                *ngIf="
                  (user$ | async)?.role === 'ADMIN' ||
                  (user$ | async)?.role === 'BACKOFFICE'
                "
                (click)="downloadModalExcel(cliente)"
              >
                <mat-icon class="text-current text-lg">table_chart</mat-icon>
              </button>
              <!-- Botón Subir Audio -->
              <button
                mat-icon-button
                class="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 transition p-1"
                [matTooltip]="'Subir audio para transcribir'"
                 
              >
                <mat-icon class="text-current text-lg">mic</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr
          mat-header-row
          *matHeaderRowDef="[
            'dni',
            'asesor',
            'fechaIngresado',
            'numeroMovil',
            'coordinador',
            'accion'
          ]"
        ></tr>
        <tr
          mat-row
          *matRowDef="
            let row;
            columns: [
              'dni',
              'asesor',
              'fechaIngresado',
              'numeroMovil',
              'coordinador',
              'accion'
            ]
          "
          class="hover:bg-gray-50 dark:hover:bg-gray-800"
        ></tr>
      </table>

      <!-- Paginador -->
      <mat-paginator
        class="bg-white dark:bg-gray-900 border-t-0 border border-gray-200 dark:border-gray-700"
        [length]="tableTotalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 20, 50]"
        [showFirstLastButtons]="true"
        (page)="handlePageEvent($event)"
      >
      </mat-paginator>
    </div>

    <!-- La sección de Gráficos por Sede se ha movido a su propia página -->

    <!-- Estado vacío -->
    <div
      *ngIf="tableData?.length === 0"
      class="flex flex-col items-center justify-center p-12 text-center bg-gray-50 dark:bg-gray-800 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg mt-6"
    >
      <mat-icon class="text-5xl text-gray-400 dark:text-gray-600 mb-4"
        >folder_off</mat-icon
      >
      <p class="text-gray-600 dark:text-gray-400">
        No hay clientes registrados
      </p>
    </div>
  </div>
</div>

<!-- Modal de Detalle -->
<div
  class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50 transition-all duration-300 ease-in-out"
  [class.hidden]="!modalVisible"
  (click)="closeModal()"
>
  <div
    class="w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900 rounded-lg shadow-xl overflow-auto animate-[zoomIn_0.3s_ease-in-out]"
    (click)="$event.stopPropagation()"
    role="dialog"
    aria-modal="true"
  >
    <div
      class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700"
    >
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
        {{ formatDateArrayWithTime((selectedCliente$ | async)?.fechaCreacion) }}
      </h2>
      <div class="flex items-center gap-2">
        <!-- Botón Excel -->
        <button
          class="p-2 rounded-full text-green-600 hover:bg-green-50 hover:text-green-700 dark:text-green-400 dark:hover:bg-gray-800 dark:hover:text-green-300 transition-colors"
          (click)="downloadModalExcel()"
          title="Descargar Excel"
          *ngIf="
            (user$ | async)?.role === 'ADMIN' ||
            (user$ | async)?.role === 'BACKOFFICE'
          "
        >
          <mat-icon>table_chart</mat-icon>
        </button>
        <!-- Botón PDF -->
        <button
          class="p-2 rounded-full text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-gray-800 dark:hover:text-red-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          (click)="downloadOrPrint('clienteDetalle')"
          title="Descargar PDF"
          [disabled]="exportLoading"
          *ngIf="
            (user$ | async)?.role === 'ADMIN' ||
            (user$ | async)?.role === 'BACKOFFICE'
          "
        >
          <mat-icon>picture_as_pdf</mat-icon>
        </button>
        <button
          class="p-2 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-white transition-colors"
          (click)="closeModal()"
        >
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <!-- Contenido del modal con id para la impresión -->
    <div class="p-3 dark:bg-gray-900">
      <div id="clienteDetalle" class="space-y-1 dark:text-gray-200">
        <ng-container
          *ngIf="
            selectedCliente$ | async as clienteDetalle;
            else loadingOrError
          "
        >
          <form [formGroup]="editForm" (ngSubmit)="onUpdateSubmit()">
            <!-- Sección 1: DATOS DEL CLIENTE -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              DATOS DEL CLIENTE
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div
                class="flex border-b border-gray-200"
                *ngIf="clienteDetalle.id"
              >
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ID:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.id }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NOMBRES Y APELLIDOS:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.nombresApellidos }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="nombresApellidos"
                    placeholder="Nombres y Apellidos"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NIF/NIE:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.nifNie }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="nifNie"
                    placeholder="NIF/NIE"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NACIONALIDAD:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.nacionalidad }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="nacionalidad"
                    placeholder="Nacionalidad"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FECHA DE NACIMIENTO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.fechaNacimiento | date : "dd/MM/yyyy" }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="date"
                    formControlName="fechaNacimiento"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FECHA DE CREACIÓN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ formatDateArrayWithTime(clienteDetalle.fechaCreacion) }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CORREO ELECTRÓNICO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.correoElectronico }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="email"
                    formControlName="correoElectronico"
                    placeholder="Correo Electrónico"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  MÓVIL DE CONTACTO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.movilContacto }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="movilContacto"
                    placeholder="Móvil de Contacto"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FIJO COMPAÑÍA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.fijoCompania }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="fijoCompania"
                    placeholder="Fijo Compañía"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  DIRECCIÓN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.direccion }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="direccion"
                    placeholder="Dirección"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CÓDIGO POSTAL:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.codigoPostal }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="codigoPostal"
                    placeholder="Código Postal"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  PROVINCIA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.provincia }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="provincia"
                    placeholder="Provincia"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  DISTRITO:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.distrito }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="distrito"
                    placeholder="Distrito"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <!--
            <div class="flex border-b border-gray-200">
              <div class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200">CIUDAD:</div>
              <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                <ng-container *ngIf="!editMode">
                  {{ clienteDetalle.ciudad }}
                </ng-container>
                <input
                  *ngIf="editMode"
                  type="text"
                  formControlName="ciudad"
                  placeholder="Ciudad"
                  class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                />
              </div>
            </div>
-->
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NÚMERO DE AGENTE:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.numeroAgente || "No especificado" }}
                </div>
              </div>
              <div
                class="flex border-b border-gray-200"
                *ngIf="clienteDetalle.usuario"
              >
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ASESOR:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  {{ clienteDetalle.usuario.nombre }}
                  {{ clienteDetalle.usuario.apellido }}
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  SUPERVISOR:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container
                    *ngIf="
                      clienteDetalle.usuario &&
                        clienteDetalle.usuario.coordinador;
                      else noCoordinador
                    "
                  >
                    {{ clienteDetalle.usuario.coordinador.nombre }}
                    {{ clienteDetalle.usuario.coordinador.apellido }}
                  </ng-container>
                  <ng-template #noCoordinador> No asignado </ng-template>
                </div>
              </div>
            </div>

            <!-- Sección 2: DATOS DE LA PROMOCIÓN -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              DATOS DE LA PROMOCIÓN
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CAMPAÑA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.campania }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="campania"
                    placeholder="Campaña"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NÚMERO MÓVILES:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.numeroMoviles }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="numeroMoviles"
                    placeholder="Número Móviles"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  PLAN ACTUAL:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.planActual }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="planActual"
                    placeholder="Plan Actual"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TIPO DE PLAN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.tipoPlan }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="tipoPlan"
                    placeholder="Tipo de Plan"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TECNOLOGÍA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.tipoTecnologia }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="tipoTecnologia"
                    placeholder="Tecnología"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  TIPO DE VELOCIDAD:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.velocidad }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="velocidad"
                    placeholder="Tipo de Velocidad"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  FÚTBOL:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    <div class="flex items-center">
                      <span
                        class="inline-flex items-center justify-center w-4 h-4 mr-1 rounded-full"
                        [class.text-green-600]="clienteDetalle.futbol === 'SI'"
                        [class.text-red-600]="clienteDetalle.futbol === 'NO'"
                        [class.text-gray-400]="
                          clienteDetalle.futbol !== 'SI' &&
                          clienteDetalle.futbol !== 'NO'
                        "
                      >
                        <svg
                          *ngIf="clienteDetalle.futbol === 'SI'"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <svg
                          *ngIf="clienteDetalle.futbol === 'NO'"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <svg
                          *ngIf="
                            clienteDetalle.futbol !== 'SI' &&
                            clienteDetalle.futbol !== 'NO'
                          "
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </span>
                      <span>{{
                        clienteDetalle.futbol || "No especificado"
                      }}</span>
                    </div>
                  </ng-container>
                  <select
                    *ngIf="editMode"
                    formControlName="futbol"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  >
                    <option value="SI">Sí</option>
                    <option value="NO">No</option>
                  </select>
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  ICC:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.icc }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="icc"
                    placeholder="ICC"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  CUENTA BANCARIA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.cuentaBancaria }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="cuentaBancaria"
                    placeholder="Cuenta Bancaria"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  PERMANENCIA:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.permanencia }}
                  </ng-container>
                  <input
                    *ngIf="editMode"
                    type="text"
                    formControlName="permanencia"
                    placeholder="Permanencia"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  />
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  MÓVILES A PORTAR:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    <!-- Asegurarse de que movilesAPortar siempre esté inicializado -->
                    <ng-container
                      *ngIf="
                        clienteDetalle.movilesAPortar &&
                          clienteDetalle.movilesAPortar.length > 0;
                        else noMoviles
                      "
                    >
                      <div class="flex flex-wrap gap-1">
                        <span
                          *ngFor="let movil of clienteDetalle.movilesAPortar"
                          class="inline-flex items-center px-1.5 py-0 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                        >
                          {{ movil }}
                        </span>
                      </div>
                    </ng-container>
                    <ng-template #noMoviles>
                      <span class="text-gray-500">No especificado</span>
                    </ng-template>
                  </ng-container>
                  <div *ngIf="editMode" class="space-y-1">
                    <div class="flex flex-wrap gap-1">
                      <span
                        *ngFor="
                          let movil of editForm.get('movilesAPortar')?.value ||
                            []
                        "
                        class="inline-flex items-center px-1.5 py-0 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                      >
                        {{ movil }}
                        <button
                          type="button"
                          (click)="removeMovil(movil)"
                          class="ml-1 text-blue-600 hover:text-blue-800"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3 w-3"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </button>
                      </span>
                    </div>
                    <div class="flex">
                      <input
                        type="text"
                        #movilInput
                        placeholder="Nuevo móvil"
                        class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                        (keyup.enter)="
                          addMovil({
                            input: movilInput,
                            value: movilInput.value
                          });
                          movilInput.value = ''
                        "
                      />
                      <button
                        type="button"
                        class="ml-2 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        (click)="
                          addMovil({
                            input: movilInput,
                            value: movilInput.value
                          });
                          movilInput.value = ''
                        "
                      >
                        Añadir
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-0 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  OBSERVACIÓN:
                </div>
                <div class="w-2/3 py-0 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.observacion }}
                  </ng-container>
                  <textarea
                    *ngIf="editMode"
                    formControlName="observacion"
                    placeholder="Observación"
                    rows="2"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Sección 3: AUTORIZACIONES Y CONFIRMACIONES -->
            <div
              class="bg-green-500 text-white text-center font-semibold py-0.5 px-4 dark:bg-green-700"
            >
              AUTORIZACIONES Y CONFIRMACIONES
            </div>
            <div
              class="bg-white border border-gray-300 mb-0.5 dark:bg-gray-800 dark:border-gray-700"
            >
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-1 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  AUTORIZA SEGUROS:
                </div>
                <div class="w-2/3 py-1 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    <div class="flex items-center">
                      <span
                        class="inline-flex items-center justify-center w-4 h-4 mr-1 rounded-full"
                        [class.text-green-600]="clienteDetalle.autorizaSeguros"
                        [class.text-red-600]="!clienteDetalle.autorizaSeguros"
                        [class.dark:text-green-500]="
                          clienteDetalle.autorizaSeguros
                        "
                        [class.dark:text-red-500]="
                          !clienteDetalle.autorizaSeguros
                        "
                      >
                        <svg
                          *ngIf="clienteDetalle.autorizaSeguros"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <svg
                          *ngIf="!clienteDetalle.autorizaSeguros"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </span>
                      <span>{{
                        clienteDetalle.autorizaSeguros ? "Sí" : "No"
                      }}</span>
                    </div>
                  </ng-container>
                  <div *ngIf="editMode" class="flex items-center">
                    <input
                      type="checkbox"
                      formControlName="autorizaSeguros"
                      class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >Autoriza Seguros</label
                    >
                  </div>
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-1 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  AUTORIZA ENERGÍAS:
                </div>
                <div class="w-2/3 py-1 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    <div class="flex items-center">
                      <span
                        class="inline-flex items-center justify-center w-4 h-4 mr-1 rounded-full"
                        [class.text-green-600]="clienteDetalle.autorizaEnergias"
                        [class.text-red-600]="!clienteDetalle.autorizaEnergias"
                        [class.dark:text-green-500]="
                          clienteDetalle.autorizaEnergias
                        "
                        [class.dark:text-red-500]="
                          !clienteDetalle.autorizaEnergias
                        "
                      >
                        <svg
                          *ngIf="clienteDetalle.autorizaEnergias"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <svg
                          *ngIf="!clienteDetalle.autorizaEnergias"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </span>
                      <span>{{
                        clienteDetalle.autorizaEnergias ? "Sí" : "No"
                      }}</span>
                    </div>
                  </ng-container>
                  <div *ngIf="editMode" class="flex items-center">
                    <input
                      type="checkbox"
                      formControlName="autorizaEnergias"
                      class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >Autoriza Energías</label
                    >
                  </div>
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-1 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  VENTA REALIZADA:
                </div>
                <div class="w-2/3 py-1 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    <div class="flex items-center">
                      <span
                        class="inline-flex items-center justify-center w-4 h-4 mr-1 rounded-full"
                        [class.text-green-600]="clienteDetalle.ventaRealizada"
                        [class.text-red-600]="!clienteDetalle.ventaRealizada"
                        [class.dark:text-green-500]="
                          clienteDetalle.ventaRealizada
                        "
                        [class.dark:text-red-500]="
                          !clienteDetalle.ventaRealizada
                        "
                      >
                        <svg
                          *ngIf="clienteDetalle.ventaRealizada"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <svg
                          *ngIf="!clienteDetalle.ventaRealizada"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </span>
                      <span>{{
                        clienteDetalle.ventaRealizada ? "Sí" : "No"
                      }}</span>
                    </div>
                  </ng-container>
                  <div *ngIf="editMode" class="flex items-center">
                    <input
                      type="checkbox"
                      formControlName="ventaRealizada"
                      class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >Venta Realizada</label
                    >
                  </div>
                </div>
              </div>
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-1 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  DESEA PROMOCIONES LOWI:
                </div>
                <div class="w-2/3 py-1 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    <div class="flex items-center">
                      <span
                        class="inline-flex items-center justify-center w-4 h-4 mr-1 rounded-full"
                        [class.text-green-600]="
                          clienteDetalle.deseaPromocionesLowi
                        "
                        [class.text-red-600]="
                          !clienteDetalle.deseaPromocionesLowi
                        "
                        [class.dark:text-green-500]="
                          clienteDetalle.deseaPromocionesLowi
                        "
                        [class.dark:text-red-500]="
                          !clienteDetalle.deseaPromocionesLowi
                        "
                      >
                        <svg
                          *ngIf="clienteDetalle.deseaPromocionesLowi"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <svg
                          *ngIf="!clienteDetalle.deseaPromocionesLowi"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </span>
                      <span>{{
                        clienteDetalle.deseaPromocionesLowi ? "Sí" : "No"
                      }}</span>
                    </div>
                  </ng-container>
                  <div *ngIf="editMode" class="flex items-center">
                    <input
                      type="checkbox"
                      formControlName="deseaPromocionesLowi"
                      class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                    />
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300"
                      >Desea promociones LOWI o costos más bajos</label
                    >
                  </div>
                </div>
              </div>
              <!-- Campo NOTA -->
              <div class="flex border-b border-gray-200">
                <div
                  class="w-1/3 py-1 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  NOTA AI:
                </div>
                <div
                  class="w-2/3 py-1 px-4 text-sm italic text-gray-600 dark:text-gray-400"
                >
                  La nota de los agentes virtuales (DISPONIBLE PROXIMAMENTE).
                </div>
              </div>
              <div
                class="flex border-b border-gray-200"
                *ngIf="clienteDetalle.observacionEstado"
              >
                <div
                  class="w-1/3 py-1 px-4 font-medium bg-gray-50 dark:bg-gray-700 dark:text-gray-200"
                >
                  OBSERVACIÓN DE ESTADO:
                </div>
                <div class="w-2/3 py-1 px-4 dark:text-gray-200">
                  <ng-container *ngIf="!editMode">
                    {{ clienteDetalle.observacionEstado }}
                  </ng-container>
                  <textarea
                    *ngIf="editMode"
                    formControlName="observacionEstado"
                    placeholder="Observación de Estado"
                    rows="2"
                    class="w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                  ></textarea>
                </div>
              </div>
            </div>

            <!-- Botones de acción -->
            <div class="flex justify-end gap-3 mt-6" *ngIf="editMode">
              <button
                type="submit"
                class="flex items-center gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white px-4 py-2 rounded-md font-medium transition shadow disabled:opacity-50 disabled:cursor-not-allowed"
                [disabled]="!editForm.valid"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span>Guardar Cambios</span>
              </button>
              <button
                type="button"
                class="flex items-center gap-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
                (click)="cancelEdit()"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                <span>Cancelar</span>
              </button>
            </div>
          </form>
        </ng-container>
      </div>

      <ng-template #loadingOrError>
        <ng-container *ngIf="loading$ | async; else errorTemplate">
          <div class="flex flex-col items-center justify-center p-8">
            <div
              class="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"
            ></div>
            <p class="text-gray-600 dark:text-gray-300">Cargando detalles...</p>
          </div>
        </ng-container>
        <ng-template #errorTemplate>
          <ng-container *ngIf="error$ | async as errorMsg">
            <div
              class="flex flex-col items-center justify-center p-8 text-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-16 w-16 text-red-500 mb-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p class="text-red-600 dark:text-red-400 mb-4">{{ errorMsg }}</p>
              <button
                type="button"
                class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
                (click)="closeModal()"
              >
                Cerrar
              </button>
            </div>
          </ng-container>
          <ng-container *ngIf="!(error$ | async)">
            <div class="flex flex-col items-center justify-center p-8">
              <div
                class="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"
              ></div>
              <p class="text-gray-600 dark:text-gray-300">
                Cargando detalles...
              </p>
            </div>
          </ng-container>
        </ng-template>
      </ng-template>
    </div>

    <!-- Footer del modal -->
    <div
      class="flex justify-end gap-3 p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-lg"
    >
      <!-- Si no estamos editando, aparece el botón "Editar" -->
      <button
        *ngIf="!editMode && (user$ | async)?.role === 'ADMIN'"
        type="button"
        class="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        (click)="enableEdit()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
        <span>Editar</span>
      </button>
      <!-- Si estamos editando, aparecen "Guardar" y "Cancelar" -->
      <button
        *ngIf="editMode"
        type="submit"
        class="flex items-center gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        <span>Guardar</span>
      </button>
      <button
        *ngIf="editMode"
        type="button"
        class="flex items-center gap-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        (click)="cancelEdit()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
        <span>Cancelar</span>
      </button>
      <!-- Botón para cerrar el modal -->
      <button
        type="button"
        class="flex items-center gap-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded-md font-medium transition shadow"
        (click)="closeModal()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
        <span>Cerrar</span>
      </button>
    </div>
  </div>
</div>
