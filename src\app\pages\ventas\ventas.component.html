<div class="ventas-container">


  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6 bg-white dark:bg-gray-900 shadow-md rounded-lg transition-all p-5">
    <div>
      <h2 class="text-xl font-semibold text-blue-600 ">Gestión de Ventas</h2>
    </div>
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
          <button (click)="abrirGuiaCreacionVentas()"
        class="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        title="Ver guía de creación de ventas">
        <mat-icon>play_circle_filled</mat-icon>
        Guía de Creación de Ventas
      </button>
      <button
        *ngIf="isAdmin() || isBackOffice()"
        routerLink="/ventas/graficos"
        class="flex items-center justify-center gap-2 bg-yellow-600 hover:bg-yellow-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow">
        <mat-icon>bar_chart</mat-icon>
        Ver Metas y Graficos
      </button>
      <button (click)="ExportarExcel(idUsuario, filterForm.value)"
        *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR() "
        class="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow">
        <mat-icon>file_download</mat-icon>
        Exportar Reporte Ventas
      </button>
      <button (click)="crearNuevaVenta()" routerLink="/ventas/crear" *ngIf="isAdmin() || isAsesor() || isCoordinador()"
        class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Nueva Venta
      </button>



    </div>
  </div>

  <!-- Filtros -->
  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6 bg-white dark:bg-gray-900 shadow-md rounded-lg transition-all p-5 w-full">
    <div class="w-full ">
      <div class="w-full flex justify-between items-center p-4 bg-gray-100 dark:bg-gray-800 rounded-md cursor-pointer"
        (click)="toggleFilters()">
        <span>Filtros</span>
        <mat-icon>{{ isFilterExpanded ? 'expand_less' : 'expand_more' }}</mat-icon>
      </div>

      <div class="w-full " [@expandCollapse]="isFilterExpanded ? 'expanded' : 'collapsed'"
        *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO() || isAsesor()">
        <form [formGroup]="filterForm" class="flex flex-col gap-2 border-box p-4">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            <!-- Campos visibles solo para roles que no son Asesor -->
            <ng-container *ngIf="!isAsesor()">
              <!-- Campo de búsqueda existente -->
              <div class="flex flex-col w-full gap-2">
                <label for="documento" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Documento del Cliente</label>
                <input type="text"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  formControlName="documento" placeholder="Ingrese número de documento">
              </div>

              <!-- Nuevo campo Segmento -->
              <div class="flex flex-col w-full gap-2">
                <label for="segmento" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Segmento</label>
                <select formControlName="segmento"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                  <option value="">Todos</option>
                  <option value="Residencial">Persona</option>
                  <option value="Empresa">Empresa</option>
                  <option value="Autonomo">Autonomo</option>

                </select>
              </div>

              <!-- Nuevo campo Estado -->
              <div class="flex flex-col w-full gap-2">
                <label for="estado" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estado</label>
                <select formControlName="estado"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                  <option value="">Todos</option>
                  <option *ngFor="let estado of estados" [value]="estado">
                    {{estado}}
                  </option>
                </select>
              </div>


              <!-- Campo Comercial como input -->
              <div class="flex flex-col w-full gap-2">
                <label for="comercial" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"><mat-icon matSuffix>person</mat-icon> Comercial</label>
                <input type="text"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  formControlName="comercial" placeholder="Ingrese nombre del comercial">

              </div>
              <div class="flex flex-col w-full gap-2">
                <label for="dni" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> <mat-icon
                    matSuffix>person</mat-icon> DNI del Agente</label>
                <input type="text"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  formControlName="dni" placeholder="Ingrese Dni del Agente">

              </div>
              <div class="flex flex-col w-full gap-2">
                <label for="movil" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> <mat-icon
                    matSuffix>person</mat-icon> Movil de Contacto</label>
                <input type="text"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  formControlName="movil" placeholder="Ingrese móvil de contacto">

              </div>
              <div class="flex flex-col w-full gap-2">
                <label for="fijo" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"><mat-icon
                    matSuffix>person</mat-icon> Número Fijo</label>
                <input type="text"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
                  formControlName="fijo" placeholder="Ingrese el número fijo">

              </div>
              <div class="flex flex-col w-full gap-2">
                <label for="codigoVenta" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"><mat-icon matSuffix>shopping_cart</mat-icon> Código de Venta</label>
                <input type="text"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500" formControlName="codigoVenta" placeholder="Ingrese el código">

              </div>
              <div class="flex flex-col w-full gap-2">
                <label for="coordinadorFiltro" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"><mat-icon matSuffix>person</mat-icon> Supervisor</label>
                <select formControlName="coordinadorFiltro" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">

                  <option value="">Todos</option>
                  <option value="JOSE MARIA TORRES CHIRINOS">Cambiar</option>
                  <option *ngFor="let coordinador of coordinadores" [value]="coordinador.nombreCoordinador">
                    {{ coordinador.nombreCoordinador }}
                  </option>
                </select>
              </div>
              <div class="flex flex-col w-full gap-2">
                <label for="sede" class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"><mat-icon matSuffix>person</mat-icon> Sede</label>
                <select formControlName="sede" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                  <option value="">Todos</option>
                  <option *ngFor="let sede of sedes" [value]="sede.nombre">
                    {{ sede.nombre }} - {{ sede.ciudad }}
                  </option>
                </select>
              </div>
               <div class="flex flex-col w-full gap-2">
                <label for="tipoVenta" class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo de Venta</label>
                <select formControlName="tipoVenta"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                  <option value="">Todos</option>
                  <option value="Televenta">Televenta</option>
                  <option value="Presencial">Presencial</option>
                </select>
              </div>
            </ng-container>

            <!-- Campos de fecha siempre visibles para todos los roles -->
            <div class="flex flex-col w-full gap-2" [ngClass]="{'col-span-3': isAsesor()}">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha Inicio</label>
              <input type="date"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500" formControlName="fechaInicio">
            </div>

            <div class="flex flex-col w-full gap-2" [ngClass]="{'col-span-3': isAsesor()}">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fecha Fin</label>
              <input type="date"
              class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500" formControlName="fechaFin">
            </div>
          </div>

          <div class="w-full flex justify-end gap-3">
            <button class="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition shadow" (click)="resetFilters()">
              <mat-icon>clear</mat-icon>
              Limpiar
            </button>
            <button class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition shadow" (click)="aplicarFiltros()">
              <mat-icon>search</mat-icon>
              Buscar
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Tabla -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
    <div class="p-6">
      <div class="relative overflow-x-auto">
        <!-- Botón para recargar tabla -->
        <div class="flex justify-end mb-4">
          <button
            class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
            (click)="recargarVentas()"
            matTooltip="Recargar tabla de ventas">
            <mat-icon>refresh</mat-icon>
            Recargar Tabla
          </button>
        </div>

        <!-- Loading spinner -->
        <div *ngIf="loading" class="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 z-10">
          <mat-spinner diameter="40" class="text-blue-600"></mat-spinner>
        </div>

        <!-- No data message -->
        <div *ngIf="!loading && ventas.data.length === 0" class="py-12 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 dark:text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-gray-500 dark:text-gray-400 text-lg">No hay ventas para mostrar</p>
        </div>

        <!-- Table -->
        <table mat-table [dataSource]="ventas" class="w-full" *ngIf="!loading && ventas.data.length > 0">
          <!-- Documento Column -->
          <ng-container matColumnDef="documento">
            <th mat-header-cell *matHeaderCellDef mat-sort-header
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Documento
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
              {{ venta.nif_nie || 'Sin documento' }}
            </td>
          </ng-container>

          <!-- Agente Column -->
          <ng-container matColumnDef="agente">
            <th mat-header-cell *matHeaderCellDef mat-sort-header
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Agente
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
              {{ venta.nombre || '' }} {{ venta.apellido || '' }}
            </td>
          </ng-container>

          <!-- Sede Column -->
          <ng-container matColumnDef="sede">
            <th mat-header-cell *matHeaderCellDef mat-sort-header
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Sede
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
              {{ venta.ciudad || 'Sin sede' }}
            </td>
          </ng-container>

          <!-- Coordinador Column -->
          <ng-container matColumnDef="coordinador">
            <th mat-header-cell *matHeaderCellDef mat-sort-header
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Supervisor
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
              {{ venta.Coordinador || 'Sin Supervisor' }}
            </td>
          </ng-container>

          <!-- DNI Column -->
          <ng-container matColumnDef="dni">
            <th mat-header-cell *matHeaderCellDef mat-sort-header
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Documento Agente
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
              {{ venta.dni || '' }}
            </td>
          </ng-container>

          <!-- Fecha/Hora Column -->
          <ng-container matColumnDef="fecha_hora">
            <th mat-header-cell *matHeaderCellDef mat-sort-header
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Fecha y Hora
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
              {{formatearFecha(venta.created_at || venta.fechaRegistro)}}
            </td>
          </ng-container>

          <!-- Acciones Column -->
          <ng-container matColumnDef="acciones">
            <th mat-header-cell *matHeaderCellDef
                class="px-6 py-4 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Acciones
            </th>
            <td mat-cell *matCellDef="let venta" class="px-6 py-4 whitespace-nowrap text-sm border-b border-gray-200 dark:border-gray-700">
              <div class="flex flex-wrap gap-2 justify-center">
                <!-- Estado Button -->
                <button
                  class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 min-w-[12rem] justify-start"
                  [ngClass]="{
                    'bg-green-100 text-green-800 hover:bg-green-200 focus:ring-green-500 dark:bg-green-800/30 dark:text-green-400': venta.Estado === 'PENDIENTE DE FILTRO',
                    'bg-red-100 text-red-800 hover:bg-red-200 focus:ring-red-500 dark:bg-red-800/30 dark:text-red-400': venta.Estado === 'FALLIDA',
                    'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-300': !venta.Estado || (venta.Estado !== 'PENDIENTE DE FILTRO' && venta.Estado !== 'FALLIDA')
                  }"
                  (click)="(isAdmin() || isBackOffice() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()) ? abrirModalEstado(venta) : null"
                  [disabled]="!(isAdmin() || isBackOffice() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO())"
                  title="Detalle de Venta">
                  <mat-icon class="h-4 w-4 mr-1">assignment</mat-icon>
                  <span class="truncate max-w-[150px]">{{ venta.Estado || 'Sin Estado' }}</span>
                </button>

                <!-- Ver Button -->
                <button
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-amber-50 text-amber-600 hover:bg-amber-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors dark:bg-amber-900/30 dark:text-amber-400"
                  (click)="verDetalle(venta)"
                  title="Ver detalle">
                  <mat-icon class="h-5 w-5">visibility</mat-icon>
                </button>

                <!-- Observación Button -->
                <button
                  *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO() || isAsesor()"
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors dark:bg-blue-900/30 dark:text-blue-400"
                  (click)="abrirModalObservacion(venta)"
                  title="Observación">
                  <mat-icon class="h-5 w-5">note_add</mat-icon>
                </button>

                <!-- Asignar Backoffice Button -->
                <button
                  *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR()"
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-green-50 text-green-600 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors dark:bg-green-900/30 dark:text-green-400"
                  (click)="abrirModalBackoffice(venta)"
                  title="Asignar Backoffice">
                  <mat-icon class="h-5 w-5">assignment_ind</mat-icon>
                </button>

                <!-- Eliminar Button -->
                <button
                  *ngIf="isAdmin()"
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-red-50 text-red-600 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors dark:bg-red-900/30 dark:text-red-400"
                  (click)="eliminarVenta(venta.codigoVenta, venta.idVenta)"
                  title="Eliminar">
                  <mat-icon class="h-5 w-5">delete</mat-icon>
                </button>

                <!-- Editar Button -->
                <button
                  *ngIf="isAdmin() || isAsesor() || isBackOffice() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO() || isCoordinador()"
                  class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-indigo-50 text-indigo-600 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors dark:bg-indigo-900/30 dark:text-indigo-400"
                  (click)="editarVenta(venta)"
                  title="Editar">
                  <mat-icon class="h-5 w-5">edit</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <!-- Header and Row Definitions -->
          <tr mat-header-row *matHeaderRowDef="displayedColumns" class="border-b border-gray-200 dark:border-gray-700"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"
              class="hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"></tr>
        </table>

        <!-- Estadísticas y Paginator -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <!-- Estadísticas de ventas -->
          <div class="flex flex-col sm:flex-row sm:items-center gap-4">
            <div class="flex items-center gap-2">
              <mat-icon class="text-blue-600 dark:text-blue-400">assessment</mat-icon>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Total: <span class="font-bold text-blue-600 dark:text-blue-400">{{ totalVentas }}</span> ventas
              </span>
            </div>

            <!-- Desglose por sede -->
            <div class="flex flex-wrap items-center gap-2" *ngIf="Object.keys(ventasPorSede).length > 0">
              <span class="text-sm text-gray-600 dark:text-gray-400">Por sede:</span>
              <div class="flex flex-wrap gap-2">
                <div
                  *ngFor="let sede of Object.keys(ventasPorSede)"
                  class="inline-flex flex-col items-start px-3 py-2 rounded-lg text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-600">

                  <!-- Nombre de la sede y total -->
                  <div class="flex items-center gap-1 font-semibold text-sm">
                    <mat-icon class="text-xs">location_on</mat-icon>
                    <span>{{ sede }}: {{ ventasPorSede[sede] }}</span>
                  </div>

                  <!-- Desglose por estado -->
                  <div class="flex flex-wrap gap-1 mt-1" *ngIf="ventasPorSedeYEstado[sede]">
                    <span
                      *ngFor="let estado of getObjectKeys(ventasPorSedeYEstado[sede])"
                      class="inline-flex items-center px-1.5 py-0.5 rounded text-xs"
                      [ngClass]="{
                        'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-400': estado === 'VALIDADA' || estado === 'CIERRE COMPLETO',
                        'bg-blue-100 text-blue-800 dark:bg-blue-800/30 dark:text-blue-400': estado === 'CIERRE PARCIAL',
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-800/30 dark:text-yellow-400': estado === 'PENDIENTE DE FILTRO' || estado === 'PDT VALIDAR',
                        'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-400': estado === 'FALLIDA' || estado === 'CANCELADO',
                        'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-300': !['VALIDADA', 'CIERRE COMPLETO', 'CIERRE PARCIAL', 'PENDIENTE DE FILTRO', 'PDT VALIDAR', 'FALLIDA', 'CANCELADO'].includes(estado)
                      }">
                      {{ estado }}: {{ ventasPorSedeYEstado[sede][estado] }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Paginator -->
          <mat-paginator
            [length]="ventas.data.length"
            [pageSize]="10"
            [pageSizeOptions]="[5, 10, 20]"
            showFirstLastButtons
            class="flex-shrink-0">
          </mat-paginator>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de Detalle de Venta -->
<div class="modal-overlay" [class.show]="modalVisible" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>{{formatearFechaHora( ventaDetalle?.fechaRegistro) }} </h2>
      <div class="header-actions">
        <!-- Botón Excel -->
        <button mat-icon-button (click)="downloadExcelDetalle(ventaDetalle.codigoVenta)" matTooltip="Descargar Excel"
          *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
          <mat-icon>table_chart</mat-icon>
        </button>
        <!-- Botón PDF existente -->
        <button mat-icon-button (click)="downloadOrPrint('ventaDetalle')" matTooltip="Descargar PDF"
          *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
          <mat-icon>download</mat-icon>
        </button>
        <button mat-icon-button (click)="closeModal()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <!-- Contenido del modal con id para la impresión -->
    <div class="modal-body" *ngIf="ventaDetalle" id="ventaDetalle">
      <!-- Sección de datos del cliente -->
      <div class="form-header">{{ventaDetalle.segmento === 'Empresa' ? 'DATOS DE LA EMPRESA' : 'DATOS DEL CLIENTE'}}
      </div>
      <div class="form-section">
        <div class="form-row">
          <div class="form-label">CAMPAÑA:</div>
          <div class="form-value">{{ ventaDetalle.campania }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">{{ ventaDetalle.segmento === 'Empresa' ? 'NOMBRE DE LA EMPRESA' : 'NOMBRES Y
            APELLIDOS' }}:</div>
          <div class="form-value">{{ ventaDetalle.nombres_apellidos }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">{{ ventaDetalle.segmento === 'Empresa' ? 'CIF' : 'NIF/NIE' }}:</div>
          <div class="form-value">{{ ventaDetalle.nif_nie }}</div>
        </div>

        <!-- Campos adicionales solo para empresa -->
        <ng-container *ngIf="ventaDetalle.segmento === 'Empresa'">
          <div class="form-row">
            <div class="form-label">NOMBRES Y APELLIDOS DEL REPRESENTANTE LEGAL:</div>
            <div class="form-value">{{ ventaDetalle.nombresApellidosRL }}</div>
          </div>
          <div class="form-row">
            <div class="form-label">NIF/NIE DEL REPRESENTANTE LEGAL:</div>
            <div class="form-value">{{ ventaDetalle.nif_nieRL }}</div>
          </div>
        </ng-container>

        <div class="form-row">
          <div class="form-label">NACIONALIDAD:</div>
          <div class="form-value">{{ ventaDetalle.nacionalidad }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">FECHA DE NACIMIENTO:</div>
          <div class="form-value">{{ ventaDetalle.nacimiento | date:'dd/MM/yyyy' }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">GÉNERO:</div>
          <div class="form-value">{{ ventaDetalle.genero }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">CUENTA BANCARIA:</div>
          <div class="form-value">{{ ventaDetalle.cuenta_bancaria }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">EMAIL:</div>
          <div class="form-value">{{ ventaDetalle.email }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">SEGMENTO:</div>
          <div class="form-value">{{ ventaDetalle.segmento }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">PERMANENCIA:</div>
          <div class="form-value">{{ ventaDetalle.permanencia }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">DIRECCIONES:</div>
          <div class="form-value">
            <div *ngFor="let dir of parseDirecciones(ventaDetalle.direcciones)">
              - {{ dir.direccion }}
            </div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-label">TIPO DE FIBRA:</div>
          <div class="form-value">{{ ventaDetalle.tipoFibra }}</div>
        </div>
      </div>

      <div class="form-header">DATOS DE LA PROMOCIÓN</div>

      <div class="form-section">
        <div class="form-row">
          <div class="form-label">PROMOCIÓN:</div>
          <div class="form-value">{{ ventaDetalle.promocion }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">TV/DECO:</div>
          <div class="form-value">{{ ventaDetalle.tvDeco }}</div>
        </div>


        <div class="form-row">
          <div class="form-label">MÓVIL CONTACTO:</div>
          <div class="form-value">{{ ventaDetalle.movilContacto }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">FIJO:</div>
          <div class="form-value">{{ ventaDetalle.telefonoCompania }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">MÓVIL A PORTAR 1/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar1 }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">MÓVIL A PORTAR 2/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar2 }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">MÓVIL A PORTAR 3/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar3 }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">MÓVIL A PORTAR 4/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar4 }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">MÓVIL A PORTAR 5/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar5 }}</div>
        </div>
        <div class="form-row" *ngIf="ventaDetalle.movilAPortar6">
          <div class="form-label">MÓVIL A PORTAR 6/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar6 }}</div>
        </div>
        <div class="form-row" *ngIf="ventaDetalle.movilAPortar7">
          <div class="form-label">MÓVIL A PORTAR 7/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar7 }}</div>
        </div>
        <div class="form-row" *ngIf="ventaDetalle.movilAPortar8">
          <div class="form-label">MÓVIL A PORTAR 8/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar8 }}</div>
        </div>
        <div class="form-row" *ngIf="ventaDetalle.movilAPortar9">
          <div class="form-label">MÓVIL A PORTAR 9/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar9 }}</div>
        </div>
        <div class="form-row" *ngIf="ventaDetalle.movilAPortar10">
          <div class="form-label">MÓVIL A PORTAR 10/COMPAÑÍA:</div>
          <div class="form-value">{{ ventaDetalle.movilAPortar10 }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">PRECIO PROMOCIÓN/TIEMPO:</div>
          <div class="form-value">{{ ventaDetalle.precioPromocionTiempo }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">PRECIO REAL/DESPUÉS DE PROMOCIÓN:</div>
          <div class="form-value">{{ ventaDetalle.precioReal }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">PRESENTACIÓN CON EL CLIENTE:</div>
          <div class="form-value">{{ ventaDetalle.presentacionCCliente }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">COMERCIAL:</div>
          <div class="form-value">{{ ventaDetalle.apellido }} {{ ventaDetalle.nombre }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">SUPERVISOR:</div>
          <div class="form-value">{{ ventaDetalle.Coordinador }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">OBSERVACIÓN:</div>
          <div class="form-value">{{ ventaDetalle.operacion }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">OPERADOR:</div>
          <div class="form-value">{{ ventaDetalle.operador }}</div>
        </div>
        <div class="form-row"
          *ngIf="isAdmin() || isBackOffice() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
          <div class="form-label">USUARIO RetailX:</div>
          <div class="form-value">{{ ventaDetalle.usuarioRetailX }}</div>
        </div>
        <div class="form-row"
          *ngIf="isAdmin() || isBackOffice() || isBACKOFFICETRAMITADOR()|| isBACKOFFICESEGUIMIENTO()">
          <div class="form-label">USUARIO SOLIVESA:</div>
          <div class="form-value">{{ ventaDetalle.usuarioSolivesa }}</div>
        </div>
        <div class="form-row">
          <div class="form-label">TIPO DE VENTA:</div>
          <div class="form-value">{{ ventaDetalle.tipoVenta }}</div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <!-- Botón Excel -->
      <button mat-raised-button color="accent"
        (click)="ventaDetalle.segmento === 'Empresa' ? downloadExcelDetalleEmpresa(ventaDetalle.codigoVenta) : downloadExcelDetalle(ventaDetalle.codigoVenta)"
        *ngIf="isAdmin() || isBackOffice() || isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
        <mat-icon>table_chart</mat-icon>
        Descargar Excel
      </button>
      <!-- Botón PDF existente -->
      <button mat-raised-button color="primary" (click)="downloadOrPrint('ventaDetalle')"
        *ngIf="isAdmin() || isBackOffice() || isCoordinador()  || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
        <mat-icon>download</mat-icon>
        Descargar PDF
      </button>
      <button mat-button color="warn" (click)="closeModal()">Cerrar</button>
    </div>
  </div>
</div>

<!-- Modal de Observación -->
<div class="modal-overlay" [class.show]="modalObservacionVisible" (click)="cerrarModalObservacion()">
  <div class="modal-content observacion-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>Observaciones</h2>
      <div class="header-actions">
        <button mat-icon-button (click)="cargarObservaciones(ventaSeleccionada.codigoVenta)"
          matTooltip="Recargar observaciones">
          <mat-icon>refresh</mat-icon>
        </button>
        <button mat-icon-button (click)="cerrarModalObservacion()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
    </div>

    <div class="modal-body">
      <!-- Lista de observaciones existentes -->
      <div class="observaciones-list">
        <mat-card *ngFor="let obs of observaciones" class="observacion-item">
          <mat-card-content>
            <p>{{obs.observacion}}</p>
            <small>Creado por: {{obs.nombre_usuario}} {{obs.apellidos_usuario}} | Fecha: {{obs.fecha |
              date:'short'}}</small>
            <small>Rol: {{obs.rol}} </small>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Formulario para nueva observación -->
      <form [formGroup]="nuevaObservacionForm" class="nueva-observacion-form">
        <div class="flex flex-col w-full gap-2"          class="full-width">
          <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nueva Observación</label>
          <textarea class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500" formControlName="observacion" rows="3" placeholder="Escriba su observación aquí">
          </textarea>
        </div>
      </form>
    </div>

    <div class="modal-footer">
      <button mat-button (click)="cerrarModalObservacion()">
        Cerrar
      </button>
      <button mat-raised-button color="primary" (click)="guardarNuevaObservacion()"
        *ngIf="isAdmin() || isBackOffice() ||   isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()"
        [disabled]="!nuevaObservacionForm.valid || guardandoObservacion">
        {{ guardandoObservacion ? 'Guardando...' : 'Guardar' }}
      </button>
    </div>
  </div>
</div>


<!-- Modal de Estado -->
<div class="modal-overlay" [class.show]="modalEstadoVisible" (click)="cerrarModalEstado()">
  <div class="modal-content estado-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>Cambiar Estado</h2>
      <button mat-icon-button (click)="cerrarModalEstado()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="modal-body">
      <form [formGroup]="estadoForm">
        <div class="flex flex-col w-full gap-2"          class="full-width">
          <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Estado</label>
          <select formControlName="estado" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
            <option *ngFor="let estado of estados" [value]="estado">
              {{ estado }}
            </option>
          </select>
        </div>
      </form>
    </div>

    <div class="modal-footer">
      <button class="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition shadow" (click)="cerrarModalEstado()">
        Cancelar
      </button>
      <button class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition shadow" color="primary" (click)="guardarEstado()"
        [disabled]="!estadoForm.valid || guardandoEstado">
        {{ guardandoEstado ? 'Guardando...' : 'Guardar' }}
      </button>
    </div>
  </div>
</div>

<!-- Modal de Edición -->
<div class="modal-overlay fixed inset-0 bg-black/50 dark:bg-black/70 z-50 flex items-center justify-center transition-opacity duration-300" [class.show]="editModalVisible" (click)="cerrarModalEdicion()">
  <div class="modal-content-editar p-0 bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-11/12 max-w-5xl max-h-[90vh] overflow-hidden" [class.dark-theme]="isDarkTheme"
    (click)="$event.stopPropagation()">
    <div class="bg-indigo-600 dark:bg-indigo-800 text-white p-5 flex justify-between items-center">
      <h2>Editar Venta</h2>
      <button mat-icon-button (click)="cerrarModalEdicion()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="modal-body">
      <form [formGroup]="editForm" (ngSubmit)="guardarCambios()">
        <!-- DATOS BÁSICOS -->
        <mat-card class="form-section">
          <mat-card-header>
            <mat-card-title>Datos Básicos</mat-card-title>
          </mat-card-header>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
            <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Código</label>
                <input type="text"
                class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500" formControlName="codigoVenta" readonly>
            </div>

            <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Campaña</label>
                <select formControlName="campania" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                  <option *ngFor="let operador of campanias" [value]="operador">
                    {{ operador }}
                  </option>
                </select>
              </div>
           </div>
        </mat-card>

        <!-- DATOS DEL CLIENTE -->
        <mat-card class="form-section">
          <mat-card-header>
            <mat-card-title>Datos del Cliente</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{{ tipoCliente === 'Empresa' ?  'Nombre de la Empresa': 'Nombres y Apellidos' }}</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="nombres_apellidos"
                  [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO() " />
              </div>

              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> {{ tipoCliente === 'Empresa' ? 'CIF' : 'NIF / NIE' }}</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="nif_nie"
                  [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()" />
              </div>
              <ng-container *ngIf="tipoCliente === 'Empresa'">
                <div class="flex flex-col w-full gap-2">
                  <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nombres y Apellidos de Representante Legal</label>
                  <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="nombresApellidosRL"
                    [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()" />
                </div>

                <div class="flex flex-col w-full gap-2">
                  <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">NIF/NIE Representante Legal</label>
                  <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="nif_nieRL"
                    [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()" />
                </div>
              </ng-container>
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nacionalidad</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="nacionalidad"
                  [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>




              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Género</label>
                <select formControlName="genero" [disabled]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500" >
                  <option value="Masculino">Masculino</option>
                  <option value="Femenino">Femenino</option>
                </select>
              </div>

              <div class="flex flex-col w-full gap-2" appearance="outline" *ngIf="isBackOffice() || isAdmin()">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Supervisor </label>
                <select formControlName="Coordinador" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                  <option *ngFor="let coordinador of coordinadores" [value]="coordinador.nombreCoordinador">
                    {{ coordinador.nombreCoordinador }}
                  </option>
                </select>
               </div>
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cuenta Bancaria</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="cuenta_bancaria"
                  [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>

              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="email" type="email"
                  [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO() ">
              </div>
                 <div class="flex flex-col w-full gap-2"
                *ngIf="isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()  || isAdmin()">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Usuario RetailX</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="usuarioRetailX" type="text">
              </div>
                 <div class="flex flex-col w-full gap-2"
                *ngIf="isCoordinador() || isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO() || isAdmin()">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Usuario Solivesa</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"  formControlName="usuarioSolivesa" type="text">
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- DATOS DEL SERVICIO -->
        <mat-card class="form-section">
          <mat-card-header>
            <mat-card-title>Datos del Servicio</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
              <!-- Permanencia -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Permanencia</label>
                <select formControlName="permanencia" [disabled]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
                  <option value="Si">Si</option>
                  <option value="No">No</option>
                </select>
              </div>
              <!-- Tipo de Fibra -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo de Fibra</label>
                <select formControlName="tipoFibra" [disabled]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
                  <option [value]="''">Ninguno</option>
                  <option *ngFor="let tipo of tiposFibra" [value]="tipo">{{ tipo }}</option>
                </select>
              </div>
              <!-- Hora de Instalación -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hora de Instalación</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="horaInstalacion" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
                 <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo de Venta</label>
   
                    <select formControlName="tipoVenta" [disabled]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()"
                                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
                                            <option value="Televenta">Televenta</option>
                                            <option value="Presencial">Presencial</option>
                                         </select>
              </div>
              <!-- Promoción -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Promoción</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="promocion" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
              <!-- TV/Deco -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">TV/Deco</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="tvDeco" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
              <!-- Operación -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Operación</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="operacion" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
              <!-- Fijo -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fijo</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="telefonoCompania" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
              <!-- Móviles a Portar 1-10 -->
              <ng-container *ngFor="let n of [1,2,3,4,5,6,7,8,9,10]">
                <div class="flex flex-col w-full gap-2">
                  <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Móvil a Portar {{n}}</label>
                  <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                    [formControlName]="'movilAPortar' + n" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
                </div>
              </ng-container>
              <!-- Precio Promoción Tiempo -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Precio Promoción Tiempo</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="precioPromocionTiempo" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
              <!-- Precio Real -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Precio Real</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="precioReal" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
              <!-- Operador -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Operador</label>
                <select formControlName="operador" [disabled]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()"
                  class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white">
                  <option *ngFor="let operador of operadores" [value]="operador">{{ operador }}</option>
                </select>
              </div>
              <!-- Presentación Cliente -->
              <div class="flex flex-col w-full gap-2">
                <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Presentación Cliente</label>
                <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
                  formControlName="presentacionCCliente" [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Direcciones -->
        <mat-card class="form-section">
          <mat-card-header>
            <mat-card-title>Dirección</mat-card-title>
          </mat-card-header>
          <div formArrayName="direcciones">
            <div *ngFor="let direccion of direccionesFormArray.controls; let i=index">
              <div class="flex  items-baseline w-full gap-4 p-4">
                <div class="flex flex-col w-full gap-2">
                  <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dirección</label>
                  <input class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white" [formControlName]="i"
                    [readonly]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
                </div>
                <button type="button" mat-icon-button color="warn" (click)="eliminarDireccion(i)"
                  [disabled]="isBACKOFFICETRAMITADOR() || isBACKOFFICESEGUIMIENTO()">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>

        </div>
          </mat-card>



        <div class="w-full flex justify-end gap-3 p-5">
          <button class="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition shadow" type="button" (click)="cerrarModalEdicion()">Cancelar</button>
          <button class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition shadow"type="submit">Guardar Cambios</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal de Asignación de Backoffice -->
<div class="modal-overlay" [class.show]="modalBackofficeVisible" (click)="cerrarModalBackoffice()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>Asignar Backoffices</h2>
      <button mat-icon-button (click)="cerrarModalBackoffice()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="modal-body">
      <form [formGroup]="backofficeForm" (ngSubmit)="asignarBackoffices()" class="flex flex-col w-full gap-4 p-4">
        <!-- Backoffice Seguimiento -->
        <div class="flex flex-col w-full gap-2"          class="full-width">
          <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Backoffice Seguimiento</label>
          <select formControlName="backoffice_seguimiento" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
            <option [value]="null">Ninguno</option>
            <option *ngFor="let back of backofficesSeguimiento" [value]="back.codi_usuario">
              <span [class.selected]="backofficeForm.get('backoffice_seguimiento')?.value === back.codi_usuario">
                {{getNombreCompleto(back)}}
              </span>
            </option>
          </select>
        </div>

        <!-- Backoffice Tramitador -->
        <div class="flex flex-col w-full gap-2" class="full-width">
          <label class="flex gap-2 justify-start items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Backoffice Tramitador</label>
          <select formControlName="backoffice_tramitador" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500">
            <option [value]="null">Ninguno</option>
            <option *ngFor="let back of backofficesTramitador" [value]="back.codi_usuario">
              <span [class.selected]="backofficeForm.get('backoffice_tramitador')?.value === back.codi_usuario">
                {{getNombreCompleto(back)}}
              </span>
            </option>
          </select>
        </div>

        <div class="modal-actions">
          <button   class="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition shadow" type="button" (click)="cerrarModalBackoffice()">Cancelar</button>
          <button class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition shadow"   type="submit">Guardar</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal de Guía de Creación de Ventas -->
<div class="modal-overlay" [class.show]="modalGuiaVisible" (click)="cerrarModalGuia()">
  <div class="modal-content guia-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2 class="text-xl font-semibold text-blue-600">Guía de Creación de Ventas</h2>
      <button type="button" mat-icon-button (click)="cerrarModalGuia()" class="text-gray-500 hover:text-gray-700">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="modal-body">
      <div class="video-container">
        <video
          *ngIf="videoGuiaUrl"
          [src]="videoGuiaUrl"
          controls
          preload="metadata"
          class="video-player"
          title="Guía de Creación de Ventas">
          Tu navegador no soporta la reproducción de videos.
        </video>

        <!-- Mensaje alternativo si no se puede cargar el video -->
        <div *ngIf="!videoGuiaUrl" class="video-placeholder">
          <mat-icon class="placeholder-icon">video_library</mat-icon>
          <p>Cargando video...</p>
        </div>
      </div>

      <div class="info-section">
        <h3>
          <mat-icon>info</mat-icon>
          Información sobre la guía
        </h3>
        <p>
          Esta guía te ayudará a entender el proceso completo de creación de ventas en el sistema.
          Asegúrate de seguir todos los pasos mostrados en el video para garantizar que las ventas
          se registren correctamente.
        </p>

        <!-- Botón de descarga alternativo -->
        <div class="download-section">
          <a
            [href]="videoGuiaUrl"
            download="guia-creacion-ventas"
            class="download-link"
            *ngIf="videoGuiaUrl">
            <mat-icon>download</mat-icon>
            Descargar video
          </a>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" mat-button color="primary" (click)="cerrarModalGuia()">
        Cerrar
      </button>
    </div>
  </div>
</div>

