<div class="google-drive-explorer">
  <!-- Header -->
  <div class="explorer-header">
    <div class="header-title">
      <h2 mat-dialog-title>{{ data.title || 'Explorador de Google Drive' }}</h2>
      <p class="subtitle" *ngIf="data.cliente">
        Cliente: {{ data.cliente.nombres }} {{ data.cliente.apellidos }}
        <span *ngIf="data.numeroMovil"> - {{ data.numeroMovil }}</span>
      </p>
      <div *ngIf="hasUploadedFiles && data.mode === 'upload'" class="upload-status">
        <mat-icon class="success-icon">check_circle</mat-icon>
        <span>{{ uploadedFilesCount }} archivo(s) subido(s) exitosamente</span>
      </div>
    </div>
    
    <div class="header-actions">
      <!-- Botón de crear carpeta -->
      <button mat-icon-button 
              matTooltip="Crear carpeta" 
              (click)="createFolder()"
              [disabled]="loading">
        <mat-icon>create_new_folder</mat-icon>
      </button>
      
      <!-- Botón de subir archivos -->
      <button mat-icon-button 
              matTooltip="Subir archivos" 
              (click)="openFileUpload()"
              [disabled]="loading || uploadLoading">
        <mat-icon>cloud_upload</mat-icon>
      </button>
      
      <!-- Toggle de vista -->
      <mat-button-toggle-group [(value)]="viewMode" class="view-toggle">
        <mat-button-toggle value="grid" matTooltip="Vista de cuadrícula">
          <mat-icon>grid_view</mat-icon>
        </mat-button-toggle>
        <mat-button-toggle value="list" matTooltip="Vista de lista">
          <mat-icon>view_list</mat-icon>
        </mat-button-toggle>
      </mat-button-toggle-group>
    </div>
  </div>

  <!-- Breadcrumbs -->
  <div class="breadcrumbs">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item" 
            *ngFor="let breadcrumb of breadcrumbs; let last = last"
            [class.active]="last">
          <a *ngIf="!last" 
             (click)="navigateToBreadcrumb(breadcrumb)"
             class="breadcrumb-link">
            <mat-icon *ngIf="breadcrumb.id === ''" class="breadcrumb-icon">home</mat-icon>
            {{ breadcrumb.name }}
          </a>
          <span *ngIf="last" class="current-folder">
            <mat-icon *ngIf="breadcrumb.id === ''" class="breadcrumb-icon">home</mat-icon>
            {{ breadcrumb.name }}
          </span>
          <mat-icon *ngIf="!last" class="separator">chevron_right</mat-icon>
        </li>
      </ol>
    </nav>
  </div>

  <!-- Barra de búsqueda -->
  <div class="search-bar">
    <mat-form-field appearance="outline" class="search-field">
      <mat-label>Buscar archivos y carpetas</mat-label>
      <input matInput 
             #searchInput
             (input)="onSearchChange($event)"
             placeholder="Escriba para buscar...">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <!-- Área de contenido principal -->
  <div class="explorer-content" 
       (dragover)="onDragOver($event)"
       (dragleave)="onDragLeave($event)"
       (drop)="onDrop($event)"
       [class.drag-over]="isDragOver">
    
    <!-- Indicador de carga -->
    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="50"></mat-spinner>
      <p>Cargando archivos...</p>
    </div>

    <!-- Indicador de subida -->
    <div *ngIf="uploadLoading" class="upload-overlay">
      <div class="upload-indicator">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Subiendo archivos...</p>
      </div>
    </div>

    <!-- Lista de archivos - Vista de cuadrícula -->
    <div *ngIf="!loading && viewMode === 'grid'" class="files-grid">
      <div *ngFor="let file of files" 
           class="file-card"
           [class.selected]="isFileSelected(file)"
           [class.folder]="file.isFolder"
           (click)="onFileClick(file)"
           (dblclick)="onFileDoubleClick(file)">
        
        <div class="file-icon">
          <mat-icon [class]="'file-icon-' + getFileIcon(file)">{{ getFileIcon(file) }}</mat-icon>
        </div>
        
        <div class="file-info">
          <div class="file-name" [title]="file.name">{{ file.name }}</div>
          <div class="file-details" *ngIf="!file.isFolder">
            <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
            <span class="file-date">{{ formatDate(file.modifiedTime) }}</span>
          </div>
        </div>

        <!-- Menú de acciones -->
        <div class="file-actions" (click)="$event.stopPropagation()">
          <!-- Botón de transcripción para archivos de audio -->
          <button *ngIf="isAudioFile(file)"
                  mat-icon-button
                  (click)="openTranscriptionDialog(file)"
                  matTooltip="Transcribir audio con IA"
                  class="transcribe-button">
            <mat-icon>record_voice_over</mat-icon>
          </button>

          <button mat-icon-button [matMenuTriggerFor]="fileMenu">
            <mat-icon>more_vert</mat-icon>
          </button>

          <mat-menu #fileMenu="matMenu">
            <button *ngIf="isAudioFile(file)" mat-menu-item (click)="openTranscriptionDialog(file)">
              <mat-icon>record_voice_over</mat-icon>
              <span>Transcribir Audio</span>
            </button>
            <button mat-menu-item (click)="previewFile(file)" *ngIf="!file.isFolder">
              <mat-icon>visibility</mat-icon>
              <span>Ver</span>
            </button>
            <button mat-menu-item (click)="downloadFile(file)" *ngIf="!file.isFolder">
              <mat-icon>download</mat-icon>
              <span>Descargar</span>
            </button>
            <button mat-menu-item (click)="deleteFile(file)">
              <mat-icon>delete</mat-icon>
              <span>Eliminar</span>
            </button>
          </mat-menu>
        </div>
      </div>
    </div>

    <!-- Lista de archivos - Vista de lista -->
    <div *ngIf="!loading && viewMode === 'list'" class="files-list">
      <table mat-table [dataSource]="files" class="files-table">
        
        <!-- Columna de selección -->
        <ng-container matColumnDef="select" *ngIf="data.mode === 'select'">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox></mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let file">
            <mat-checkbox [checked]="isFileSelected(file)"
                         (change)="toggleFileSelection(file)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Columna de nombre -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>Nombre</th>
          <td mat-cell *matCellDef="let file" 
              (click)="onFileClick(file)"
              (dblclick)="onFileDoubleClick(file)"
              class="file-name-cell">
            <div class="file-name-container">
              <mat-icon class="file-type-icon">{{ getFileIcon(file) }}</mat-icon>
              <span class="file-name">{{ file.name }}</span>
            </div>
          </td>
        </ng-container>

        <!-- Columna de tamaño -->
        <ng-container matColumnDef="size">
          <th mat-header-cell *matHeaderCellDef>Tamaño</th>
          <td mat-cell *matCellDef="let file">
            {{ file.isFolder ? '-' : formatFileSize(file.size || 0) }}
          </td>
        </ng-container>

        <!-- Columna de fecha de modificación -->
        <ng-container matColumnDef="modified">
          <th mat-header-cell *matHeaderCellDef>Modificado</th>
          <td mat-cell *matCellDef="let file">
            {{ formatDate(file.modifiedTime) }}
          </td>
        </ng-container>

        <!-- Columna de acciones -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Acciones</th>
          <td mat-cell *matCellDef="let file">
            <!-- Botón de transcripción para archivos de audio -->
            <button *ngIf="isAudioFile(file)"
                    mat-icon-button
                    (click)="openTranscriptionDialog(file)"
                    matTooltip="Transcribir audio con IA"
                    class="transcribe-button">
              <mat-icon>record_voice_over</mat-icon>
            </button>

            <button mat-icon-button [matMenuTriggerFor]="fileMenu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #fileMenu="matMenu">
              <button *ngIf="isAudioFile(file)" mat-menu-item (click)="openTranscriptionDialog(file)">
                <mat-icon>record_voice_over</mat-icon>
                <span>Transcribir Audio</span>
              </button>
              <button mat-menu-item (click)="previewFile(file)" *ngIf="!file.isFolder">
                <mat-icon>visibility</mat-icon>
                <span>Ver</span>
              </button>
              <button mat-menu-item (click)="downloadFile(file)" *ngIf="!file.isFolder">
                <mat-icon>download</mat-icon>
                <span>Descargar</span>
              </button>
              <button mat-menu-item (click)="deleteFile(file)">
                <mat-icon>delete</mat-icon>
                <span>Eliminar</span>
              </button>
            </mat-menu>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="getDisplayedColumns()"></tr>
        <tr mat-row *matRowDef="let row; columns: getDisplayedColumns();"
            [class.selected]="isFileSelected(row)"></tr>
      </table>
    </div>

    <!-- Mensaje cuando no hay archivos -->
    <div *ngIf="!loading && files.length === 0" class="empty-state">
      <mat-icon class="empty-icon">folder_open</mat-icon>
      <h3 *ngIf="!currentFolderId">No hay carpetas disponibles</h3>
      <h3 *ngIf="currentFolderId">No hay archivos en esta carpeta</h3>
      <p *ngIf="searchQuery">No se encontraron elementos que coincidan con "{{ searchQuery }}"</p>
      <p *ngIf="!searchQuery && !currentFolderId">No se encontraron carpetas en Google Drive. Puedes crear una nueva carpeta.</p>
      <p *ngIf="!searchQuery && currentFolderId">Esta carpeta está vacía. Puedes subir archivos o crear subcarpetas.</p>
    </div>

    <!-- Overlay de drag & drop -->
    <div *ngIf="isDragOver" class="drag-overlay">
      <div class="drag-content">
        <mat-icon class="drag-icon">cloud_upload</mat-icon>
        <h3>Suelta los archivos aquí</h3>
        <p>Los archivos se subirán a la carpeta actual</p>
      </div>
    </div>
  </div>

  <!-- Controles de paginación -->
  <div *ngIf="!loading && files.length > 0" class="pagination-controls">
    <div class="pagination-info">
      <span>Página {{ currentPage + 1 }}</span>
      <span *ngIf="totalPages > 0"> de {{ totalPages }}</span>
      <span *ngIf="totalItems > 0"> ({{ totalItems }} elementos total)</span>
    </div>

    <div class="pagination-buttons">
      <button mat-icon-button
              [disabled]="!hasPreviousPage"
              (click)="goToFirstPage()"
              matTooltip="Primera página">
        <mat-icon>first_page</mat-icon>
      </button>

      <button mat-icon-button
              [disabled]="!hasPreviousPage"
              (click)="previousPage()"
              matTooltip="Página anterior">
        <mat-icon>chevron_left</mat-icon>
      </button>

      <button mat-icon-button
              [disabled]="!hasNextPage"
              (click)="nextPage()"
              matTooltip="Página siguiente">
        <mat-icon>chevron_right</mat-icon>
      </button>
    </div>
  </div>

  <!-- Footer con acciones -->
  <div class="explorer-footer" mat-dialog-actions>
    <div class="footer-info">
      <span *ngIf="selectedFiles.length > 0">
        {{ selectedFiles.length }} archivo(s) seleccionado(s)
      </span>
      <span *ngIf="files.length > 0">
        {{ files.length }} elemento(s) en esta página
        <span *ngIf="!currentFolderId" class="content-type">(carpetas)</span>
        <span *ngIf="currentFolderId" class="content-type">(archivos y carpetas)</span>
      </span>
    </div>
    
    <div class="footer-actions">
      <button mat-button (click)="cancel()">Cancelar</button>

      <button *ngIf="data.mode === 'upload'"
              mat-raised-button
              color="accent"
              (click)="closeAfterUpload()"
              matTooltip="Cerrar después de subir archivos">
        Finalizar
      </button>

      <button *ngIf="data.mode === 'select'"
              mat-raised-button
              color="primary"
              [disabled]="selectedFiles.length === 0"
              (click)="selectAndClose()">
        Seleccionar ({{ selectedFiles.length }})
      </button>
    </div>
  </div>

  <!-- Input oculto para selección de archivos -->
  <input #fileInput 
         type="file" 
         multiple 
         style="display: none"
         [accept]="data.allowedTypes?.join(',')"
         (change)="onFileSelected($event)">
</div>
