import { Component, OnInit, Input, HostListener } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { VentaService, VentaRequest } from '@app/services/venta.service';
import { Router, ActivatedRoute } from '@angular/router';
import { User } from '@app/models/backend';
import { GeneralService } from '@app/services/general.service';
import { DireccionService } from '@app/services/direccion.service';
import Swal from 'sweetalert2';
import { Coordinador } from '../../../models/backend/user/index';
import { MatRadioChange } from '@angular/material/radio';
import { LogService } from '@app/services/log.service';
import { CatastroService } from '@app/services/catastro.service';
import { MapDialogComponent } from '@app/pages/mapas/map-dialog/map-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { debounceTime, distinctUntilChanged, switchMap, filter } from 'rxjs/operators';

interface DireccionPromocion {
  direccion: string;
  promocion: string;
  tvDeco: string;
  telefonoCompania: string;
  movilContacto: string;
  movilAPortar1: string;
  movilAPortar2: string;
  movilAPortar3: string;
  movilAPortar4: string;
  movilAPortar5: string;
  movilAPortar6: string;
  movilAPortar7: string;
  movilAPortar8: string;
  movilAPortar9: string;
  movilAPortar10: string;
  GBmovilAPortar1: string;
  GBmovilAPortar2: string;
  GBmovilAPortar3: string;
  GBmovilAPortar4: string;
  GBmovilAPortar5: string;
  GBmovilAPortar6: string;
  GBmovilAPortar7: string;
  GBmovilAPortar8: string;
  GBmovilAPortar9: string;
  GBmovilAPortar10: string;

  precioPromocionTiempo: string;
  precioReal: string;
  presentacionCCliente: string;
  operador: string;
  operacion: string ;
}

@Component({
  selector: 'app-crear-venta',
  templateUrl: './crear-venta.component.html',
  styleUrls: ['./crear-venta.component.scss']
})
export class CrearVentaComponent implements OnInit {
  ventaForm!: FormGroup;
  loading = false;
  codigo: string = '';
  campanias: string[] = ['Vodafone', 'Lowi'];
  tipoCliente: string = 'Residencial';
  roleUser: string = '';
  tiposFibra: string[] = ['No tiene', '300', '600', '1GB', 'ROUTER 5G'];
  operadores: string[] = [
    'ADAMO', 'ALEMOBIL', 'AVATEL', 'EUSKALTEL', 'FINETWORK', 'MASS MOVIL',
    'GELPIU', 'GUUK', 'HITS MOBILE', 'JAZZTEL', 'LOWI',
    'MÁSMÓVIL', 'MOVISTAR', 'O2', 'ORANGE', 'PEPEPHONE',
    'R CABLE', 'SILBÖ', 'SIMYO', 'VODAFONE', 'YOIGO',
    'DIGI SPAIN TELECOM', 'LEMONVIL', 'R CABLE Y TELECOMUNICACIONES',
    'AIRENETWORKS', 'PTV TELECOM 5G', 'PEPEPHONE 3.0', 'TELECABLE',
    'LLAMAYA_GMM', 'LAYCA', 'VIRGIN', '7PLAY', 'SEVEN', 'DIGI',
    'LEBARA', 'AMENA', 'PARLEM', 'HAPPYMOVIL'
  ];

  isEditing = false;
  @Input() user: User | null = null;
  idUsuario: number | null | string = null;
  nombreComercial: null | string = '';
  nombreCoordinador: null | string = '';
  movilesVisibles: number = 5;

  // Propiedades para el selector de fecha de registro
  fechaRegistroInput: string = '';
  fechaRegistroSeleccionada: 'hoy' | 'ayer' = 'hoy';
  fechaRegistro: Date = new Date();

  // Propiedades para el formulario de dirección
  provinciaFilterControl = new FormControl('');
  municipioFilterControl = new FormControl('');
  viaFilterControl = new FormControl('');
  selectedViaCatastro: any = null;

  selectedProvinciaCatastro: any = null;
  selectedMunicipioCatastro: any = null;

  provinciasCatastro: any[] = [];
  municipiosCatastro: any[] = [];
  viasCatastro: any[] = [];

  provinciasCatastroFiltradas: any[] = [];
  municipiosCatastroFiltrados: any[] = [];
  viasCatastroFiltradas: any[] = [];

  mostrarProvincias: boolean = false;
  mostrarMunicipios: boolean = false;
  mostrarVias: boolean = false;

  cargandoProvincias: boolean = false;
  cargandoMunicipios: boolean = false;
  cargandoVias: boolean = false;

  // Objeto para almacenar los datos del formulario
  formData = {
    clienteResidencial: {
      provincia: '',
      distrito: '', // municipio
      numero: '',
      bloque: '',
      escalera: '',
      planta: '',
      puerta: '',
      codigoPostal: '',
      direccion: ''
    }
  };

  // Array para almacenar los datos de dirección para cada promoción
  direccionesData: Array<{
    provincia: string;
    distrito: string; // municipio
    numero: string;
    bloque: string;
    escalera: string;
    planta: string;
    puerta: string;
    codigoPostal: string;
    direccion: string;
    via: string;
  }> = [];

  constructor(
    private generalService: GeneralService,
    private fb: FormBuilder,
    private ventaService: VentaService,
    private router: Router,
    private route: ActivatedRoute,
    private direccionService: DireccionService,
    private logService: LogService,
    private catastroService: CatastroService,
      private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.initForm();
    this.setupUserInfo();
    this.checkEditMode();
    this.cargarProvincias(); // Cargar provincias al iniciar
    this.initializeFechaRegistro(); // Inicializar fecha de registro

    // Inicializar el array de direcciones con una dirección por defecto
    this.direccionesData = [{
      provincia: '',
      distrito: '',
      numero: '',
      bloque: '',
      escalera: '',
      planta: '',
      puerta: '',
      codigoPostal: '',
      direccion: '',
      via: ''
    }];

    const usuario = this.generalService.usuario$;

    if (usuario) {
      this.roleUser = usuario.role || '';

     } else {
      const userFromLocalStorage = localStorage.getItem('user');
      if (userFromLocalStorage) {
        const user = JSON.parse(userFromLocalStorage);
        this.roleUser = user.role || '';

       } else {
        console.warn('No se encontró información del usuario.');
      }
    }

    // Configurar filtro de provincias - filtrar cuando el usuario escribe
    this.provinciaFilterControl.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(filtro => {
        this.cargandoProvincias = true;
        this.mostrarProvincias = true; // Mostrar dropdown al escribir
        return this.catastroService.getProvincias(filtro || '');
      })
    ).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.provinciasCatastro = response.d;
        }
        this.cargandoProvincias = false;
      },
      error: (error) => {
        //console.error('Error al filtrar provincias:', error);
        this.cargandoProvincias = false;
      }
    });

    // Configurar filtro de municipios - filtrar cuando el usuario escribe
    this.municipioFilterControl.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(() => this.selectedProvinciaCatastro), // Solo procesar si hay provincia seleccionada
      switchMap(filtro => {
        this.cargandoMunicipios = true;
        this.mostrarMunicipios = true; // Mostrar dropdown al escribir
        return this.catastroService.getMunicipios(this.selectedProvinciaCatastro.Codigo );
      })
    ).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.municipiosCatastro = response.d;
        }
        this.cargandoMunicipios = false;
      },
      error: (error) => {
        //console.error('Error al filtrar municipios:', error);
        this.cargandoMunicipios = false;
      }
    });

    // Configurar filtro de vías - filtrar cuando el usuario escribe
    this.viaFilterControl.valueChanges.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      filter(() => this.selectedProvinciaCatastro && this.selectedMunicipioCatastro), // Solo procesar si hay provincia y municipio seleccionados
      switchMap(filtro => {
        this.cargandoVias = true;
        this.mostrarVias = true; // Mostrar dropdown al escribir
        return this.catastroService.getVias(
          this.selectedProvinciaCatastro.Codigo,
          this.selectedMunicipioCatastro.Codigo,
          filtro || ''
        );
      })
    ).subscribe({
      next: (response) => {
        if (response && response.d) {
          this.viasCatastro = response.d;
        }
        this.cargandoVias = false;
      },
      error: (error) => {
        //console.error('Error al filtrar vías:', error);
        this.cargandoVias = false;
      }
    });
  }

  private initForm(): void {
    this.ventaForm = this.fb.group({
      codigoVenta: [{value: '', disabled: true}],
      campania: [''],
      nombres_apellidos: ['' ,[Validators.required]],
      nif_nie: ['', [Validators.required]],
      nacionalidad: [''],
      nacimiento: [''],
      genero: [''],
      cuenta_bancaria: [''],
      Coordinador: [''],
      segmento: [''],
      email: ['', [Validators.required, Validators.email]],
      permanencia: [''],
      fechaRegistro: [new Date()], // Agregar campo de fecha de registro

      idUsuario: [this.idUsuario],
      documento: [''],
      nombresApellidosRL: [''],
      nif_nieRL: [''],
      usuarioRetailX: [''],
      usuarioSolivesa: [''],
      direccionesPromociones: this.fb.array([
        this.crearDireccionPromocionGroup()
      ])
    });
  }
//
  private crearDireccionPromocionGroup(): FormGroup {
    return this.fb.group({
      direccion: [''],
      promocion: [''],
      tvDeco: [''],
      telefonoCompania: [''],
      movilContacto: [''],
      movilAPortar1: [''],
      movilAPortar2: [''],
      movilAPortar3: [''],
      movilAPortar4: [''],
      movilAPortar5: [''],
      movilAPortar6: [''],
      movilAPortar7: [''],
      movilAPortar8: [''],
      movilAPortar9: [''],
      movilAPortar10: [''],
      GBmovilAPortar1: [''],
      GBmovilAPortar2: [''],
      GBmovilAPortar3: [''],
      GBmovilAPortar4: [''],
      GBmovilAPortar5: [''],
      GBmovilAPortar6: [''],
      GBmovilAPortar7: [''],
      GBmovilAPortar8: [''],
      GBmovilAPortar9: [''],
      GBmovilAPortar10: [''],
      tipoFibra: [''],
      horaInstalacion: [''],
      precioPromocionTiempo: [''],
      precioReal: [''],
      presentacionCCliente: [''],
      operador: [''],
      operacion: [''],
      tipoVenta: ['']
    });
  }

  get direccionesPromociones(): FormArray {
    return this.ventaForm.get('direccionesPromociones') as FormArray;
  }

  private setupUserInfo(): void {
    const usuario = this.generalService.usuario$;
    const userFromLocalStorage2 = localStorage.getItem('coordinador');

    let coordinador = null;
    if (userFromLocalStorage2 &&
        userFromLocalStorage2 !== 'undefined' &&
        userFromLocalStorage2 !== 'null') {
      try {
        coordinador = JSON.parse(userFromLocalStorage2);
      } catch (e) {
        console.warn('Error al parsear el coordinador:', e);
      }
    }

    if (usuario) {
      this.setUserDetails(usuario, coordinador);
    } else {
      this.loadUserFromLocalStorage(coordinador);
    }
  }

  private setUserDetails(usuario: any, coordinador: any): void {
    this.idUsuario = usuario.id || '';
    this.nombreComercial = `${usuario.apellido} ${usuario.nombre}` || '';
     if(usuario.role === 'COORDINADOR'){
this.nombreCoordinador =   `${usuario.apellido} ${usuario.nombre}` || ''
    }else{
this.nombreCoordinador = `${coordinador?.apellido || ''} ${coordinador?.nombre || ''}`
    }
   }

  private loadUserFromLocalStorage(coordinador: any): void {
    const userFromLocalStorage = localStorage.getItem('user');

    if (userFromLocalStorage) {
      try {
        const user = JSON.parse(userFromLocalStorage);
        this.setUserDetails(user, coordinador);
      } catch (e) {
        console.warn('Error al parsear el usuario:', e);
      }
    } else {
      console.warn('No se encontró información del usuario.');
    }
  }

  private checkEditMode(): void {
    const codigoVenta = this.route.snapshot.params['codigo'];
    if (codigoVenta) {
      this.isEditing = true;
      this.codigo = codigoVenta;
    } else {
      this.getCode();
    }
  }

  agregarDireccionPromocion(): void {
    // Agregar un nuevo grupo de dirección promoción al formulario
    this.direccionesPromociones.push(this.crearDireccionPromocionGroup());

    // Agregar un nuevo objeto de datos de dirección al array
    this.direccionesData.push({
      provincia: '',
      distrito: '',
      numero: '',
      bloque: '',
      escalera: '',
      planta: '',
      puerta: '',
      codigoPostal: '',
      direccion: '',
      via: ''
    });

    console.log('Nueva dirección agregada. Total direcciones:', this.direccionesData.length);
  }

  eliminarDireccionPromocion(index: number): void {
    if (this.direccionesPromociones.length > 1) {
      // Eliminar el grupo de dirección promoción del formulario
      this.direccionesPromociones.removeAt(index);

      // Eliminar los datos de dirección del array
      if (index < this.direccionesData.length) {
        this.direccionesData.splice(index, 1);
      }

      console.log('Dirección eliminada. Total direcciones:', this.direccionesData.length);
    }
  }

  onSubmit(): void {
    if (this.loading || !this.ventaForm.valid) return;

    // Verificar que todas las direcciones estén completas
    const direccionesIncompletas = this.direccionesData.filter(dir => !dir.direccion || dir.direccion.trim() === '');

    if (direccionesIncompletas.length > 0) {
      Swal.fire({
        icon: 'warning',
        title: 'Direcciones incompletas',
        text: 'Debe completar todas las direcciones antes de guardar la venta',
        confirmButtonColor: '#d33'
      });
      return;
    }

    this.guardarVenta();
  }

  private async guardarVenta(): Promise<void> {
    this.loading = true;
    const formValue = this.ventaForm.getRawValue();
    const segmento = this.tipoCliente  ;
    const datosCliente = {
      campania: formValue.campania,
      nombres_apellidos: formValue.nombres_apellidos,
      nif_nie: formValue.nif_nie,
      nacionalidad: formValue.nacionalidad,
      nacimiento: formValue.nacimiento ? this.formatDate(formValue.nacimiento) : null,
      genero: formValue.genero,
      cuenta_bancaria: formValue.cuenta_bancaria,
      email: formValue.email,
      permanencia: formValue.permanencia,
      tipoFibra: formValue.tipoFibra,
      horaInstalacion: formValue.horaInstalacion,
      idUsuario: this.idUsuario,
      Coordinador: this.nombreCoordinador,
      segmento: segmento,
      nombresApellidosRL: formValue.nombresApellidosRL,
      nif_nieRL: formValue.nif_nieRL,
      operacion: formValue.operacion,
      usuarioRetailX: formValue.usuarioRetailX,
      usuarioSolivesa: formValue.usuarioSolivesa,
    };

    const direccionesPromociones = formValue.direccionesPromociones;

    try {
      // Procesar las ventas secuencialmente con un retraso
      for (let index = 0; index < direccionesPromociones.length; index++) {
        const dirProm = direccionesPromociones[index];
        const userFromLocalStorage = localStorage.getItem('user') || '';
        const user = JSON.parse(userFromLocalStorage);

        // Usar la dirección completa específica para esta promoción
        const direccionCompleta = index < this.direccionesData.length ?
          this.direccionesData[index].direccion :
          (this.formData.clienteResidencial.direccion || dirProm.direccion);

        const ventaRequest = {
          ...datosCliente,
          idUsuario: user.id,
          Coordinador: this.nombreCoordinador,
          nacimiento: formValue.nacimiento ?
            this.formatDate(formValue.nacimiento) : null,
          fechaRegistro: this.formatDateForDatabase(this.fechaRegistro), // Formatear fecha para la base de datos
          direccion: direccionCompleta,
          promocion: dirProm.promocion,
          tvDeco: dirProm.tvDeco,
          telefonoCompania: dirProm.telefonoCompania,
          movilContacto: dirProm.movilContacto,
          movilAPortar1: dirProm.movilAPortar1,
          movilAPortar2: dirProm.movilAPortar2,
          movilAPortar3: dirProm.movilAPortar3,
          movilAPortar4: dirProm.movilAPortar4,
          movilAPortar5: dirProm.movilAPortar5,
          movilAPortar6: dirProm.movilAPortar6,
          movilAPortar7: dirProm.movilAPortar7,
          movilAPortar8: dirProm.movilAPortar8,
          movilAPortar9: dirProm.movilAPortar9,
          movilAPortar10: dirProm.movilAPortar10,
          GBmovilAPortar1: dirProm.GBmovilAPortar1,
          GBmovilAPortar2: dirProm.GBmovilAPortar2,
          GBmovilAPortar3: dirProm.GBmovilAPortar3,
          GBmovilAPortar4: dirProm.GBmovilAPortar4,
          GBmovilAPortar5: dirProm.GBmovilAPortar5,
          GBmovilAPortar6: dirProm.GBmovilAPortar6,
          GBmovilAPortar7: dirProm.GBmovilAPortar7,
          GBmovilAPortar8: dirProm.GBmovilAPortar8,
          GBmovilAPortar9: dirProm.GBmovilAPortar9,
          GBmovilAPortar10: dirProm.GBmovilAPortar10,


          precioPromocionTiempo: dirProm.precioPromocionTiempo,
          precioReal: dirProm.precioReal,
          presentacionCCliente: dirProm.presentacionCCliente,
          operador: dirProm.operador,
          operacion: dirProm.operacion,
          tipoFibra: dirProm.tipoFibra,
          horaInstalacion: dirProm.horaInstalacion,
          tipoVenta: dirProm.tipoVenta,
        };

        // Crear la venta y esperar su respuesta
        await new Promise((resolve) => {
          this.ventaService.crearVenta(ventaRequest as VentaRequest).subscribe({
            next: async (response) => {
              try {
                // Crear la dirección usando la dirección completa
                await this.direccionService.crearDireccion({
                  codigoVenta: response.codigoVenta,
                  direccion: direccionCompleta // Usar la dirección completa
                }).toPromise();
                this.logService.logCreacion(
                  'Ventas',
                  'Ventas',
                  response.idVenta || 1,
                  JSON.stringify(response)
                ).subscribe();
                // Esperar 1 segundo antes de la siguiente venta
                await new Promise(r => setTimeout(r, 1000));
                resolve(true);
              } catch (error) {
                console.error('Error al crear la dirección:', error);
                Swal.fire({
                  icon: 'error',
                  title: 'Error',
                  text: 'Error al registrar la dirección',
                  confirmButtonColor: '#d33'
                });
                resolve(false);
              }
            },
            error: (error) => {
              console.error('Error al guardar:', error);
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Error al registrar la venta',
                confirmButtonColor: '#d33'
              });
              resolve(false);
            }
          });
        });
      }

      // Todas las ventas se completaron
      this.loading = false;
      Swal.fire({
        icon: 'success',
        title: '¡Éxito!',
        text: 'Ventas guardadas correctamente',
        confirmButtonColor: '#3085d6'
      });

      this.router.navigate(['/ventas']);

    } catch (error) {
      this.loading = false;
      console.error('Error al guardar las ventas:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Error al registrar las ventas',
        confirmButtonColor: '#d33'
      });
    }
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    return new Date(date).toISOString().split('T')[0];
  }

  private getCode(): void {
    this.ventaService.generateCode().subscribe({
      next: (response) => {
        this.codigo = response.code;
        this.ventaForm.patchValue({ codigoVenta: this.codigo });
      },
      error: (error) => {
        console.error('Error al generar código:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudo generar el código',
          confirmButtonColor: '#d33'
        });
      }
    });
  }

  resetForm(): void {
    this.ventaForm.reset();
    this.getCode();
  }

  onTipoClienteChange(event: MatRadioChange): void {
    this.tipoCliente = event.value;
    const segmento = event.value ;

    this.ventaForm.patchValue({
      segmento: segmento
    });

    if (event.value === 'Residencial') {
      this.movilesVisibles = 5;
      this.ventaForm.patchValue({
        nombresApellidosRL: '',
        nif_nieRL: ''
      });
    }
  }

  mostrarSiguienteMovil(): void {
    if (this.movilesVisibles < 10) {
      this.movilesVisibles++;
    }
  }
  isCoordinador(): boolean {
    return this.roleUser === 'COORDINADOR';
  }

  /**
   * Valida que solo se ingresen números en los campos de teléfono
   * @param event Evento del teclado
   */
  validateNumberInput(event: KeyboardEvent): void {
    // Permitir solo teclas numéricas (0-9) y teclas de control (backspace, delete, etc.)
    if (!/^\d$/.test(event.key) &&
        event.key !== 'Backspace' &&
        event.key !== 'Delete' &&
        event.key !== 'ArrowLeft' &&
        event.key !== 'ArrowRight' &&
        event.key !== 'Tab') {
      event.preventDefault();
    }
  }

  /**
   * Valida que solo se peguen números en los campos de teléfono
   * @param event Evento de pegado
   */
  validatePaste(event: ClipboardEvent): void {
    const clipboardData = event.clipboardData?.getData('text') || '';
    if (!/^\d+$/.test(clipboardData)) {
      event.preventDefault();
    }
  }

  // Métodos para el formulario de dirección
  // Cargar provincias
  cargarProvincias(): void {
    console.log('Cargando provincias...');
    this.cargandoProvincias = true;
    this.catastroService.getProvincias('').subscribe({
      next: (response) => {
         this.provinciasCatastro = response;
        this.provinciasCatastroFiltradas = [...response]; // Inicializar lista filtrada
        this.cargandoProvincias = false;

        // Mostrar el dropdown de provincias
        this.mostrarProvincias = true;
      },
      error: (error) => {
        console.error('Error al cargar provincias:', error);
        this.cargandoProvincias = false;
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudieron cargar las provincias',
          confirmButtonColor: '#d33'
        });
      }
    });
  }

  // Filtrar provincias según lo que escribe el usuario
  filtrarProvincias(event: Event): void {
    const input = event.target as HTMLInputElement;
    const filtro = input.value.toLowerCase();

    console.log('Filtrando provincias con:', filtro);

    // Mostrar el dropdown al escribir
    this.mostrarProvincias = true;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    // Si no hay provincias cargadas, cargarlas
    if (this.provinciasCatastro.length === 0) {
      console.log('No hay provincias cargadas, cargando...');
      this.cargarProvincias();
      return;
    }

    // Filtrar provincias
    if (filtro) {
      console.log('Filtrando de', this.provinciasCatastro.length, 'provincias');
      this.provinciasCatastroFiltradas = this.provinciasCatastro.filter(
        provincia => provincia.Denominacion.toLowerCase().includes(filtro)
      );

      console.log('Provincias filtradas:', this.provinciasCatastroFiltradas);

      // Si hay una coincidencia exacta, seleccionarla automáticamente
      const coincidenciaExacta = this.provinciasCatastro.find(
        provincia => provincia.Denominacion.toLowerCase() === filtro
      );

      if (coincidenciaExacta) {
        console.log('Coincidencia exacta encontrada:', coincidenciaExacta);
        this.seleccionarProvinciaCatastro(coincidenciaExacta);
      }
    } else {
      this.provinciasCatastroFiltradas = [...this.provinciasCatastro];
    }

    console.log('Filtrando provincias:', filtro, 'Resultados:', this.provinciasCatastroFiltradas.length);
  }

  // Toggle dropdown de provincias
   toggleProvinciasDropdown(event: Event) {
    event.stopPropagation();
    event.preventDefault();

    // Cerrar otros dropdowns
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    // Siempre mostrar el dropdown de provincias
    this.mostrarProvincias = true;

    // Siempre cargar las provincias al hacer clic
    this.cargandoProvincias = true;

    // Pequeño retraso para evitar que aparezca el autocompletador del navegador
    setTimeout(() => {
      this.catastroService.getProvincias(this.provinciaFilterControl.value || '').subscribe({
        next: (response) => {
          if (response && response.d) {
            this.provinciasCatastro = response.d;
          }
          this.cargandoProvincias = false;
        },
        error: (error) => {
          console.error('Error al cargar provincias:', error);
          this.cargandoProvincias = false;
        }
      });
    }, 10);
  }

  // Seleccionar provincia
  seleccionarProvinciaCatastro(provincia: any, index: number = 0): void {
    console.log('Seleccionando provincia:', provincia, 'para índice:', index);
    this.selectedProvinciaCatastro = provincia;

    // Asegurar que el índice sea válido
    if (index >= 0 && index < this.direccionesData.length) {
      // Actualizar el array de direcciones
      this.direccionesData[index].provincia = provincia.Denominacion;

      // Resetear municipio y vía para este índice
      this.direccionesData[index].distrito = '';
      this.direccionesData[index].via = '';
    }

    this.mostrarProvincias = false;

    // Resetear municipio y vía globales
    this.selectedMunicipioCatastro = null;
    this.municipioFilterControl.setValue('');
    this.viaFilterControl.setValue('');
    this.municipiosCatastro = [];
    this.viasCatastro = [];

    // Cargar municipios de la provincia seleccionada
    this.cargarMunicipios(provincia.Codigo);

    // Actualizar dirección completa
    this.actualizarDireccionCompleta(index);
  }

  // Cargar municipios
  cargarMunicipios(codigoProvincia: string): void {
    console.log('Cargando municipios para provincia:', codigoProvincia);
    this.cargandoMunicipios = true;

    // Usar el método getMunicipios en lugar de getMunicipiosByProvincia
    this.catastroService.getMunicipios(codigoProvincia).subscribe({
      next: (response) => {
        console.log('Municipios cargados:', response);
        this.municipiosCatastro = response;
        this.municipiosCatastroFiltrados = [...response]; // Inicializar lista filtrada
        this.cargandoMunicipios = false;

        // Mostrar el dropdown de municipios
        this.mostrarMunicipios = true;
      },
      error: (error) => {
        console.error('Error al cargar municipios:', error);
        this.cargandoMunicipios = false;
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudieron cargar los municipios',
          confirmButtonColor: '#d33'
        });
      }
    });
  }

  // Filtrar municipios según lo que escribe el usuario
  filtrarMunicipios(event: Event): void {
    const input = event.target as HTMLInputElement;
    const filtro = input.value.toLowerCase();

    console.log('Filtrando municipios con:', filtro);

    // Si no hay provincia seleccionada, no hacer nada
    if (!this.selectedProvinciaCatastro) {
      console.log('No hay provincia seleccionada');
      return;
    }

    // Mostrar el dropdown al escribir
    this.mostrarMunicipios = true;
    this.mostrarProvincias = false;
    this.mostrarVias = false;

    // Si no hay municipios cargados, cargarlos
    if (this.municipiosCatastro.length === 0) {
      console.log('No hay municipios cargados, cargando...');
      this.cargarMunicipios(this.selectedProvinciaCatastro.Codigo);
      return;
    }

    // Filtrar municipios
    if (filtro) {
      console.log('Filtrando de', this.municipiosCatastro.length, 'municipios');
      this.municipiosCatastroFiltrados = this.municipiosCatastro.filter(
        municipio => municipio.Denominacion.toLowerCase().includes(filtro)
      );

      console.log('Municipios filtrados:', this.municipiosCatastroFiltrados);

      // Si hay una coincidencia exacta, seleccionarla automáticamente
      const coincidenciaExacta = this.municipiosCatastro.find(
        municipio => municipio.Denominacion.toLowerCase() === filtro
      );

      if (coincidenciaExacta) {
        console.log('Coincidencia exacta encontrada:', coincidenciaExacta);
        this.seleccionarMunicipioCatastro(coincidenciaExacta);
      }
    } else {
      this.municipiosCatastroFiltrados = [...this.municipiosCatastro];
    }

    console.log('Filtrando municipios:', filtro, 'Resultados:', this.municipiosCatastroFiltrados.length);
  }

  // Toggle dropdown de municipios
  toggleMunicipiosDropdown(event: Event) {
    event.stopPropagation();
    event.preventDefault();

    // Si no hay provincia seleccionada, no hacer nada
    if (!this.selectedProvinciaCatastro) {
      return;
    }

    // Cerrar otros dropdowns
    this.mostrarProvincias = false;
    this.mostrarVias = false;

    // Siempre mostrar el dropdown de municipios
    this.mostrarMunicipios = true;

    // Siempre cargar los municipios al hacer clic
    this.cargandoMunicipios = true;

    // Pequeño retraso para evitar que aparezca el autocompletador del navegador
    setTimeout(() => {
      this.catastroService.getMunicipios(
        this.selectedProvinciaCatastro.Codigo

      ).subscribe({
        next: (response) => {
          if (response && response.d) {
            this.municipiosCatastro = response.d;
          }
          this.cargandoMunicipios = false;
        },
        error: (error) => {
          console.error('Error al cargar municipios:', error);
          this.cargandoMunicipios = false;
        }
      });
    }, 10);
  }

  // Seleccionar municipio
  seleccionarMunicipioCatastro(municipio: any, index: number = 0): void {
    console.log('Seleccionando municipio:', municipio, 'para índice:', index);
    this.selectedMunicipioCatastro = municipio;

    // Asegurar que el índice sea válido
    if (index >= 0 && index < this.direccionesData.length) {
      // Actualizar el array de direcciones
      this.direccionesData[index].distrito = municipio.Denominacion;

      // Resetear vía para este índice
      this.direccionesData[index].via = '';
    }

    this.mostrarMunicipios = false;

    // Resetear vía global
    this.viaFilterControl.setValue('');
    this.viasCatastro = [];
    this.viasCatastroFiltradas = [];

    // Cargar vías del municipio seleccionado
    this.buscarViasCatastro();

    // Actualizar dirección completa
    this.actualizarDireccionCompleta(index);
  }

  toggleViasDropdown(event: Event) {
    event.stopPropagation();
    event.preventDefault();

    // Si no hay municipio seleccionado, no hacer nada
    if (!this.selectedProvinciaCatastro || !this.selectedMunicipioCatastro) {
      return;
    }

    // Cerrar otros dropdowns
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;

    // Siempre mostrar el dropdown de vías
    this.mostrarVias = true;

    // Siempre cargar las vías al hacer clic
    this.cargandoVias = true;

    // Pequeño retraso para evitar que aparezca el autocompletador del navegador
    setTimeout(() => {
      this.catastroService.getVias(
        this.selectedProvinciaCatastro.Codigo,
        this.selectedMunicipioCatastro.Codigo,
        this.viaFilterControl.value || ''
      ).subscribe({
        next: (response) => {
          if (response && response.d) {
            this.viasCatastro = response.d;
          }
          this.cargandoVias = false;
        },
        error: (error) => {

          this.cargandoVias = false;
        }
      });
    }, 10);
  }

  @HostListener('document:click', ['$event'])
  cerrarDropdowns(event: MouseEvent): void {
    // No cerrar los dropdowns si se hace clic en un input o en un dropdown
    const target = event.target as HTMLElement;
    if (target.closest('.provincia-dropdown') ||
        target.closest('.municipio-dropdown') ||
        target.closest('.via-dropdown')) {
      return;
    }

    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;
  }

  // Método para seleccionar una vía
  seleccionarViaCatastro(via: any, index: number = 0) {
    this.selectedViaCatastro = via;
    console.log('Seleccionando vía:', via, 'para índice:', index);

    // Asegurar que el índice sea válido
    if (index >= 0 && index < this.direccionesData.length) {
      // Actualizar el array de direcciones
      this.direccionesData[index].via = via.DenominacionCompleta;
    }

    // Ocultar el dropdown de vías - forzar cierre inmediato
    this.mostrarVias = false;

    // Actualizar la dirección completa con la provincia, municipio y vía seleccionados
    this.actualizarDireccionCompleta(index);

    console.log('Vía seleccionada:', via.DenominacionCompleta, 'Código:', via.Codigo);

    // Aplicar un pequeño retraso para asegurar que el DOM se actualice
    setTimeout(() => {
      this.mostrarVias = false;
    }, 100);
  }

  buscarViasCatastro(): void {
    if (!this.selectedMunicipioCatastro) {
      console.log('No hay municipio seleccionado para buscar vías');
      return;
    }

    console.log('Buscando vías para municipio:', this.selectedMunicipioCatastro.Codigo);
    this.cargandoVias = true;

    // Usar el método getVias en lugar de getViasByMunicipio
    this.catastroService.getVias(
      this.selectedProvinciaCatastro.Codigo,
      this.selectedMunicipioCatastro.Codigo
    ).subscribe({
      next: (response) => {
        console.log('Vías cargadas:', response);
        this.viasCatastro = response;
        this.viasCatastroFiltradas = [...response]; // Inicializar lista filtrada
        this.cargandoVias = false;

        // Mostrar el dropdown de vías
        this.mostrarVias = true;
      },
      error: (error) => {

        this.cargandoVias = false;

      }
    });
  }

  // Filtrar vías según lo que escribe el usuario
  filtrarVias(event: Event): void {
    const input = event.target as HTMLInputElement;
    const filtro = input.value.toLowerCase();

    console.log('Filtrando vías con:', filtro);

    // Si no hay municipio seleccionado, no hacer nada
    if (!this.selectedMunicipioCatastro) {
      console.log('No hay municipio seleccionado');
      return;
    }

    // Mostrar el dropdown al escribir
    this.mostrarVias = true;
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;

    // Si no hay vías cargadas, cargarlas
    if (this.viasCatastro.length === 0) {
      console.log('No hay vías cargadas, cargando...');
      this.buscarViasCatastro();
      return;
    }

    // Filtrar vías
    if (filtro) {
      console.log('Filtrando de', this.viasCatastro.length, 'vías');
      this.viasCatastroFiltradas = this.viasCatastro.filter(
        via => via.DenominacionCompleta.toLowerCase().includes(filtro)
      );

      console.log('Vías filtradas:', this.viasCatastroFiltradas);

      // Si hay una coincidencia exacta, seleccionarla automáticamente
      const coincidenciaExacta = this.viasCatastro.find(
        via => via.DenominacionCompleta.toLowerCase() === filtro
      );

      if (coincidenciaExacta) {
        console.log('Coincidencia exacta encontrada:', coincidenciaExacta);
        this.seleccionarViaCatastro(coincidenciaExacta);
      }
    } else {
      this.viasCatastroFiltradas = [...this.viasCatastro];
    }

    console.log('Filtrando vías:', filtro, 'Resultados:', this.viasCatastroFiltradas.length);
  }

   actualizarDireccionCompleta(index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];
    let direccionCompleta = '';

    console.log('Actualizando dirección completa para índice:', index, 'con datos:', direccionData);

    // Agregar la vía si está seleccionada, formateando el tipo de vía correctamente
    if (this.selectedViaCatastro) {
      // Extraer el tipo de vía y el nombre
      let tipoVia = '';
      let nombreVia = '';

      if (this.selectedViaCatastro.TipoVia) {
        tipoVia = this.selectedViaCatastro.TipoVia.toLowerCase();
        // Convertir primera letra a mayúscula
        tipoVia = tipoVia.charAt(0).toUpperCase() + tipoVia.slice(1);
      }

      // Si la denominación completa incluye el tipo de vía entre paréntesis
      const match = this.selectedViaCatastro.DenominacionCompleta.match(/(.*)\s*\((.*)\)/);
      if (match) {
        nombreVia = match[1].trim();
        if (!tipoVia) {
          tipoVia = match[2].trim().toLowerCase();
          tipoVia = tipoVia.charAt(0).toUpperCase() + tipoVia.slice(1);
        }
      } else {
        nombreVia = this.selectedViaCatastro.DenominacionCompleta;
      }

      // Formatear como "Tipo Nombre" (ej: "Calle Mayor")
      if (tipoVia && nombreVia) {
        direccionCompleta = `${tipoVia} ${nombreVia}`;
      } else {
        direccionCompleta = this.selectedViaCatastro.DenominacionCompleta;
      }

      direccionData.via = direccionCompleta;
    } else if (this.viaFilterControl.value) {
      // Intentar extraer tipo de vía si está en formato "NOMBRE (TIPO)"
      const match = this.viaFilterControl.value.match(/(.*)\s*\((.*)\)/);
      if (match) {
        const nombreVia = match[1].trim();
        const tipoVia = match[2].trim().toLowerCase();
        const tipoViaFormatted = tipoVia.charAt(0).toUpperCase() + tipoVia.slice(1);
        direccionCompleta = `${tipoViaFormatted} ${nombreVia}`;
      } else {
        direccionCompleta = this.viaFilterControl.value;
      }
      direccionData.via = direccionCompleta;
    } else if (direccionData.via) {
      direccionCompleta = direccionData.via;
    }

    // Agregar el número si existe
    if (direccionData.numero) {
      if (direccionCompleta) {
        // Agregar coma y el número
        direccionCompleta += ', N° ' + direccionData.numero;
      } else {
        // Si no hay vía, usar solo el número
        direccionCompleta = 'N°' + direccionData.numero;
      }
    }

    // Agregar los detalles de dirección interna si existen
    let detallesInternos = '';

    // Bloque
    if (direccionData.bloque) {
      detallesInternos += 'Bloque ' + direccionData.bloque;
    }

    // Escalera
    if (direccionData.escalera) {
      if (detallesInternos) {
        detallesInternos += ', ';
      }
      detallesInternos += 'Escalera ' + direccionData.escalera;
    }

    // Planta
    if (direccionData.planta) {
      if (detallesInternos) {
        detallesInternos += ', ';
      }
      detallesInternos += 'Planta ' + direccionData.planta;
    }

    // Puerta
    if (direccionData.puerta) {
      if (detallesInternos) {
        detallesInternos += ', ';
      }
      detallesInternos += 'Puerta ' + direccionData.puerta;
    }

    // Agregar los detalles internos a la dirección
    if (detallesInternos) {
      if (direccionCompleta) {
        direccionCompleta += ' ,' + detallesInternos + '';
      } else {
        direccionCompleta = detallesInternos;
      }
    }

    // Agregar el municipio si está seleccionado
    if (this.selectedMunicipioCatastro) {
      direccionData.distrito = this.selectedMunicipioCatastro.Denominacion;
      if (direccionCompleta) {
        direccionCompleta += ', ' + this.selectedMunicipioCatastro.Denominacion;
      } else {
        direccionCompleta = this.selectedMunicipioCatastro.Denominacion;
      }
    } else if (this.municipioFilterControl.value) {
      direccionData.distrito = this.municipioFilterControl.value;
      if (direccionCompleta) {
        direccionCompleta += ', ' + this.municipioFilterControl.value;
      } else {
        direccionCompleta = this.municipioFilterControl.value;
      }
    } else if (direccionData.distrito) {
      if (direccionCompleta) {
        direccionCompleta += ', ' + direccionData.distrito;
      } else {
        direccionCompleta = direccionData.distrito;
      }
    }

    // Agregar la provincia si está seleccionada
    if (this.selectedProvinciaCatastro) {
      direccionData.provincia = this.selectedProvinciaCatastro.Denominacion;
      if (direccionCompleta) {
        direccionCompleta += ', ' + this.selectedProvinciaCatastro.Denominacion;
      } else {
        direccionCompleta = this.selectedProvinciaCatastro.Denominacion;
      }
    } else if (this.provinciaFilterControl.value) {
      direccionData.provincia = this.provinciaFilterControl.value;
      if (direccionCompleta) {
        direccionCompleta += ', ' + this.provinciaFilterControl.value;
      } else {
        direccionCompleta = this.provinciaFilterControl.value;
      }
    } else if (direccionData.provincia) {
      if (direccionCompleta) {
        direccionCompleta += ', ' + direccionData.provincia;
      } else {
        direccionCompleta = direccionData.provincia;
      }
    }

    // Agregar el código postal si existe
    const codigoPostal = direccionData.codigoPostal?.trim();
    if (codigoPostal) {
      if (direccionCompleta) {
        direccionCompleta += ', ' + codigoPostal;
      } else {
        direccionCompleta = ' ' + codigoPostal;
      }
    }

    console.log('Dirección completa generada:', direccionCompleta);

    // Actualizar el campo de dirección en el objeto de datos
    direccionData.direccion = direccionCompleta;

    // Actualizar el campo de dirección en el formulario reactivo para la dirección específica
    const direccionesPromociones = this.ventaForm.get('direccionesPromociones') as FormArray;
    if (direccionesPromociones && direccionesPromociones.length > index) {
      const direccionPromocion = direccionesPromociones.at(index) as FormGroup;
      direccionPromocion.get('direccion')?.setValue(direccionCompleta);
      console.log('Dirección actualizada para índice', index, ':', direccionCompleta);
    } else {
      console.warn('No se pudo actualizar la dirección para el índice', index);
    }
  }

   // Método para manejar cambios en el código postal
  onCodigoPostalInput(event: Event, index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;
    value = value.replace(/\D/g, ''); // Solo números
    if (value.length > 5) {
      value = value.slice(0, 5);
    }

    // Actualizar el código postal en el objeto de datos
    direccionData.codigoPostal = value;

    // Si el código postal tiene 5 dígitos, buscar el municipio
    if (value.length === 5) {
      console.log('Buscando municipio por CP:', value, 'para índice:', index);
      this.catastroService.getMunicipioByCP(value).subscribe({
        next: (response) => {
          console.log('Respuesta de búsqueda por CP:', response);
          if (response && response.length > 0) {
            this.selectedMunicipioCatastro = response[0];
            this.municipioFilterControl.setValue(response[0].Denominacion);
            direccionData.distrito = response[0].Denominacion;

            // Si también viene la provincia, actualizarla
            if (response[0].Provincia) {
              this.selectedProvinciaCatastro = response[0].Provincia;
              this.provinciaFilterControl.setValue(response[0].Provincia.Denominacion);
              direccionData.provincia = response[0].Provincia.Denominacion;
            }

            // Limpiar las vías cargadas anteriormente
            this.viasCatastro = [];
            this.viasCatastroFiltradas = [];
            this.viaFilterControl.setValue('');

            // Cargar vías del municipio
            this.buscarViasCatastro();
          } else {
            console.warn('No se encontraron municipios para el CP:', value);
            this.selectedMunicipioCatastro = null;
          }
        },
        error: (error) => {
          console.error('Error al buscar municipio por CP:', error);
          this.selectedMunicipioCatastro = null;
        }
      });
    }

    this.actualizarDireccionCompleta(index);
  }

  onEscaleraInput(event: Event, index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Actualizar la escalera en el objeto de datos
    direccionData.escalera = value;

    this.actualizarDireccionCompleta(index);
  }

  onPlantaInput(event: Event, index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Actualizar la planta en el objeto de datos
    direccionData.planta = value;

    this.actualizarDireccionCompleta(index);
  }

  onPuertaInput(event: Event, index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Actualizar la puerta en el objeto de datos
    direccionData.puerta = value;

    this.actualizarDireccionCompleta(index);
  }

  onBloqueInput(event: Event, index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Actualizar el bloque en el objeto de datos
    direccionData.bloque = value;

    this.actualizarDireccionCompleta(index);
  }

  onNumeroInput(event: Event, index: number = 0) {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;
    value = value.replace(/\D/g, ''); // Solo números

    // Actualizar el número en el objeto de datos
    direccionData.numero = value;

    this.actualizarDireccionCompleta(index);
  }
    verEnMapa(index: number = 0) {
      // Asegurarse de que el índice sea válido
      if (index < 0 || index >= this.direccionesData.length) {
        console.warn('Índice de dirección inválido:', index);
        return;
      }

      // Obtener los datos de dirección para este índice
      const direccionData = this.direccionesData[index];

      console.log('los datos para el mapa son', direccionData, this.selectedViaCatastro);

      // Verificar que estén completos los campos de provincia, municipio y vía
      // Permitir abrir el mapa si tenemos los valores en el formulario, aunque no tengamos los objetos seleccionados
      const viaValue = this.selectedViaCatastro ? this.selectedViaCatastro.DenominacionCompleta :
                      (this.viaFilterControl.value || direccionData.via);

      if (!direccionData.provincia || !direccionData.distrito || !viaValue) {
        Swal.fire({
          icon: 'warning',
          title: 'Información incompleta',
          text: 'Debes seleccionar provincia, municipio y vía para ver el mapa',
          confirmButtonColor: '#3085d6',
        });
        return;
      }

      // Preparar los parámetros básicos para el mapa (comunes a ambos tipos)
      const queryParams: { [key: string]: string } = {
        provincia: direccionData.provincia,
        municipio: direccionData.distrito,
        via: viaValue,
        codigoPostal: direccionData.codigoPostal || '',
        // Añadir siempre los parámetros adicionales de dirección para ambos tipos de mapa
        numero: direccionData.numero || '',
        bloque: direccionData.bloque || '',
        escalera: direccionData.escalera || '',
        planta: direccionData.planta || '',
        puerta: direccionData.puerta || ''
      };

      // Si tenemos los códigos de provincia y municipio, los incluimos para la API del Catastro
      if (this.selectedProvinciaCatastro) {
        queryParams['codigoProvincia'] = this.selectedProvinciaCatastro.Codigo;
      }

      if (this.selectedMunicipioCatastro) {
        queryParams['codigoMunicipio'] = this.selectedMunicipioCatastro.Codigo;
      }

      // Mostrar diálogo para elegir el tipo de mapa
      Swal.fire({
        title: 'Seleccionar tipo de mapa',
        text: 'Elige el tipo de mapa que deseas utilizar',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#2196f3',
        confirmButtonText: 'Mapa Leaflet',
        cancelButtonText: 'Mapa Mapbox',
        reverseButtons: true
      }).then((result) => {
        // Determinar el tipo de mapa seleccionado
        let mapType: 'leaflet' | 'mapbox';

        if (result.isConfirmed) {
          // Usar Leaflet (Nominatim/Catastro)
          mapType = 'leaflet';
        } else if (result.dismiss === Swal.DismissReason.cancel) {
          // Usar Mapbox
          mapType = 'mapbox';
        } else {
          // El usuario cerró el diálogo sin elegir
          return;
        }

        // Verificar si el tema oscuro está activo
        const isDarkTheme = document.body.classList.contains('dark-theme');

        // Abrir el diálogo con el mapa seleccionado
        const dialogRef = this.dialog.open(MapDialogComponent, {
          maxWidth: '100vw',
          maxHeight: '100vh',
          height: '100%',
          width: '100%',
          panelClass: ['fullscreen-dialog', isDarkTheme ? 'dark-theme' : ''],
          data: {
            mapType: mapType,
            queryParams: queryParams
          }
        });

        // Suscribirse al evento de cierre del diálogo para recibir los datos actualizados
        dialogRef.afterClosed().subscribe(result => {
          if (result) {
            // Actualizar los campos de dirección con los valores recibidos del diálogo
            this.formData.clienteResidencial.bloque = result.bloque || this.formData.clienteResidencial.bloque;
            this.formData.clienteResidencial.escalera = result.escalera || this.formData.clienteResidencial.escalera;
            this.formData.clienteResidencial.planta = result.planta || this.formData.clienteResidencial.planta;
            this.formData.clienteResidencial.puerta = result.puerta || this.formData.clienteResidencial.puerta;
            this.formData.clienteResidencial.numero = result.numero || this.formData.clienteResidencial.numero;

            // Actualizar el código postal si se recibió uno nuevo
            if (result.codigoPostal) {
              this.formData.clienteResidencial.codigoPostal = result.codigoPostal;

            }

            // Actualizar provincia, municipio y vía si se recibieron nuevos valores
            if (result.provincia) {
              this.formData.clienteResidencial.provincia = result.provincia;
              // Si cambia la provincia, actualizar el control de filtro y la selección
              this.provinciaFilterControl.setValue(result.provincia, {emitEvent: false});
              // Buscar la provincia en la lista para actualizar la selección
              if (this.provinciasCatastro.length > 0) {
                this.selectedProvinciaCatastro = this.provinciasCatastro.find(
                  p => p.Denominacion === result.provincia
                );
              }

              // Si no encontramos la provincia en la lista, crear un objeto personalizado
              if (!this.selectedProvinciaCatastro && result.provincia) {
                this.selectedProvinciaCatastro = {
                  Denominacion: result.provincia,
                  Codigo: '0' // Código ficticio
                };
              }
            }

            if (result.municipio) {
              this.formData.clienteResidencial.distrito = result.municipio;
              // Si cambia el municipio, actualizar el control de filtro y la selección
              this.municipioFilterControl.setValue(result.municipio, {emitEvent: false});
              // Buscar el municipio en la lista para actualizar la selección
              if (this.municipiosCatastro.length > 0) {
                this.selectedMunicipioCatastro = this.municipiosCatastro.find(
                  m => m.Denominacion === result.municipio
                );
              }

              // Si no encontramos el municipio en la lista, crear un objeto personalizado
              if (!this.selectedMunicipioCatastro && result.municipio) {
                this.selectedMunicipioCatastro = {
                  Denominacion: result.municipio,
                  Codigo: '0' // Código ficticio
                };
              }
            }

            if (result.via) {
              // Si cambia la vía, actualizar el control de filtro y la selección
              this.viaFilterControl.setValue(result.via, {emitEvent: false});
              // Buscar la vía en la lista para actualizar la selección
              if (this.viasCatastro.length > 0) {
                this.selectedViaCatastro = this.viasCatastro.find(
                  v => v.DenominacionCompleta === result.via
                );
              }

              // Si no encontramos la vía en la lista, crear un objeto personalizado
              if (!this.selectedViaCatastro && result.via) {
                this.selectedViaCatastro = {
                  DenominacionCompleta: result.via,
                  Denominacion: result.via,
                  Codigo: '0', // Código ficticio
                  TipoVia: ''  // Tipo de vía vacío
                };
              }
            }

            // Actualizar la dirección completa
            // Ahora siempre deberíamos tener los objetos seleccionados
            this.actualizarDireccionCompleta();
          }
        });
      });
    }

  // Método para actualizar la provincia cuando el usuario escribe directamente
  actualizarProvinciaManual(index: number = 0, event?: Event): void {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    // Obtener el valor del campo, ya sea del evento o del control
    let valor = '';
    if (event) {
      const inputElement = event.target as HTMLInputElement;
      valor = inputElement.value;
    } else {
      valor = this.provinciaFilterControl.value || '';
    }

    // Actualizar la provincia en el objeto de datos
    direccionData.provincia = valor;

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta(index);
  }

  // Método para actualizar el municipio cuando el usuario escribe directamente
  actualizarMunicipioManual(index: number = 0, event?: Event): void {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    // Obtener el valor del campo, ya sea del evento o del control
    let valor = '';
    if (event) {
      const inputElement = event.target as HTMLInputElement;
      valor = inputElement.value;
    } else {
      valor = this.municipioFilterControl.value || '';
    }

    // Actualizar el municipio en el objeto de datos
    direccionData.distrito = valor;

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta(index);
  }

  // Método para actualizar la vía cuando el usuario escribe directamente
  actualizarViaManual(index: number = 0, event?: Event): void {
    // Asegurarse de que el índice sea válido
    if (index < 0 || index >= this.direccionesData.length) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener los datos de dirección para este índice
    const direccionData = this.direccionesData[index];

    // Obtener el valor del campo, ya sea del evento o del control
    let valor = '';
    if (event) {
      const inputElement = event.target as HTMLInputElement;
      valor = inputElement.value;
    } else {
      valor = this.viaFilterControl.value || '';
    }

    // Actualizar la vía en el objeto de datos
    direccionData.via = valor;

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta(index);
  }

  // Métodos para manejar la fecha de registro
  private initializeFechaRegistro(): void {
    // Inicializar con la fecha de hoy
    this.seleccionarFechaRegistro('hoy');
  }

  seleccionarFechaRegistro(opcion: 'hoy' | 'ayer'): void {
    this.fechaRegistroSeleccionada = opcion;

    const fecha = new Date();

    if (opcion === 'ayer') {
      // Restar un día para obtener ayer
      fecha.setDate(fecha.getDate() - 1);
    }

    // Establecer la hora a las 00:00:00
    fecha.setHours(0, 0, 0, 0);

    // Actualizar las propiedades
    this.fechaRegistro = fecha;
    this.fechaRegistroInput = this.formatDateForInput(fecha);

    // Actualizar el formulario
    if (this.ventaForm) {
      this.ventaForm.patchValue({
        fechaRegistro: fecha
      });
    }
  }

  private formatDateForInput(date: Date): string {
    // Formatear la fecha para el input type="date" (YYYY-MM-DD)
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  private formatDateForDatabase(date: Date): string {
    // Formatear la fecha para la base de datos MySQL (YYYY-MM-DD HH:MM:SS)
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}
