import { Component } from '@angular/core';
import { TranscriptionDialogLauncherService } from './transcription-dialog-launcher.service';

@Component({
  selector: 'app-transcription-example',
  template: `
    <div class="p-6">
      <h2 class="text-2xl font-bold mb-6">Ejemplo de Uso del Modal de Transcripción</h2>
      
      <div class="space-y-4">
        <!-- Botón para abrir modal con subida de archivos -->
        <button 
          type="button"
          mat-raised-button 
          color="primary" 
          (click)="openTranscriptionModal()"
          class="w-full">
          <mat-icon>record_voice_over</mat-icon>
          Transcribir Audio (Subir Archivo)
        </button>

        <!-- Botón para abrir modal con datos de cliente -->
        <button 
          type="button"
          mat-raised-button 
          color="accent" 
          (click)="openTranscriptionModalWithClient()"
          class="w-full">
          <mat-icon>person</mat-icon>
          Transcribir Audio para Cliente Específico
        </button>

        <!-- Información de uso -->
        <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 class="text-lg font-medium text-blue-800 mb-2">Cómo usar:</h3>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>• Haz clic en cualquier botón para abrir el modal</li>
            <li>• Arrastra y suelta un archivo de audio o haz clic para seleccionar</li>
            <li>• Configura las opciones de transcripción</li>
            <li>• Haz clic en "Iniciar Transcripción"</li>
            <li>• Espera a que se complete el proceso</li>
          </ul>
        </div>

        <!-- Formatos soportados -->
        <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 class="text-lg font-medium text-green-800 mb-2">Formatos Soportados:</h3>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm text-green-700">
            <span class="bg-green-100 px-2 py-1 rounded">MP3</span>
            <span class="bg-green-100 px-2 py-1 rounded">WAV</span>
            <span class="bg-green-100 px-2 py-1 rounded">M4A</span>
            <span class="bg-green-100 px-2 py-1 rounded">OGG</span>
            <span class="bg-green-100 px-2 py-1 rounded">FLAC</span>
          </div>
          <p class="text-sm text-green-600 mt-2">Tamaño máximo: 100MB</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .space-y-4 > * + * {
      margin-top: 1rem;
    }
    .space-y-1 > * + * {
      margin-top: 0.25rem;
    }
    .gap-2 {
      gap: 0.5rem;
    }
  `]
})
export class TranscriptionExampleComponent {

  constructor(
    private transcriptionLauncher: TranscriptionDialogLauncherService
  ) {}

  /**
   * Abre el modal de transcripción básico
   */
  openTranscriptionModal(): void {
    this.transcriptionLauncher.openTranscriptionDialog({
      allowFileUpload: true
    }).subscribe(result => {
      if (result && result.success) {
        console.log('Transcripción completada:', result);
        // Aquí puedes manejar el resultado de la transcripción
      }
    });
  }

  /**
   * Abre el modal de transcripción con datos de cliente
   */
  openTranscriptionModalWithClient(): void {
    this.transcriptionLauncher.openTranscriptionDialog({
      cliente: {
        nombres: 'Juan Carlos',
        apellidos: 'Pérez García'
      },
      numeroMovil: '123456789',
      allowFileUpload: true
    }).subscribe(result => {
      if (result && result.success) {
        console.log('Transcripción completada para cliente:', result);
        // Aquí puedes manejar el resultado de la transcripción
      }
    });
  }
}
