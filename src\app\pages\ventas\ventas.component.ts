import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { HttpClient } from '@angular/common/http';
import { VentaService } from '../../services/venta.service';
import { DireccionService, Direccion } from '../../services/direccion.service';
import { Store } from '@ngrx/store';
import * as fromRoot from '../../store';
import { GeneralService } from '../../services/general.service';
import Swal from 'sweetalert2';
import { Observacion } from '../../services/venta.service';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { User } from '@app/models/backend';
import { environment } from '@src/environments/environment';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Backoffice } from '../../models/backoffice.interface';
import { forkJoin, Subscription } from 'rxjs';
 import { Sede } from '@app/models/backend/sede/sede.model';
import { SedeService } from '@app/services/sede.service';
import { LogService } from '@app/services/log.service';

@Component({
  selector: 'app-ventas',
  templateUrl: './ventas.component.html',
  styleUrls: ['./ventas.component.scss'],
  animations: [
    trigger('expandCollapse', [
      state('collapsed', style({
        height: '0',
        overflow: 'hidden',
        opacity: '0'
      })),
      state('expanded', style({
        height: '*',
        opacity: '1'
      })),
      transition('expanded <=> collapsed', animate('300ms ease-in-out'))
    ])
  ]
})

export class VentasComponent implements OnInit {
  filterForm: FormGroup;
  ventas = new MatTableDataSource<any>();
  displayedColumns: string[] = [];
  loading = true;
  modalVisible = false;
  ventaDetalle: any = null;
  editandoVenta: any = null;
  @Input() user: User | null = null;
  roleUser: string = '';
  idUsuario: number| null | string = null
  campanias: string[] = ['Vodafone' ];
  isDarkTheme = false;
  sedes: Sede[] = [];
  tipoCliente: string = 'Residencial';
  // Listados fijos
  tiposFibra: string[] = ['No tiene', '300', '600', '1GB', 'ROUTER 5G'];
  operadores: string[] = [
    'ADAMO',
    'ALEMOBIL',
    'AVATEL',
    'EUSKALTEL',
    'FINETWORK',
    'GELPIU',
    'GUUK',
    'HITS MOBILE',
    'JAZZTEL',
    'LOWI',
    'MÁSMÓVIL',
    'MOVISTAR',
    'O2',
    'ORANGE',
    'PEPEPHONE',
    'R CABLE',
    'SILBÖ',
    'SIMYO',
    'VODAFONE',
    'YOIGO',
    // Nuevos elementos que no estaban en la lista original:
    'DIGI SPAIN TELECOM',
    'LEMONVIL',
    'R CABLE Y TELECOMUNICACIONES',
    'AIRENETWORKS',
    'PTV TELECOM 5G',
    'PEPEPHONE 3.0',
    'TELECABLE',
    'LLAMAYA_GMM',
    'LAYCA',
    'VIRGIN',
    '7PLAY',
    'SEVEN',
    'DIGI',
    'LEBARA',
    'AMENA',
    'PARLEM',
    'HAPPYMOVIL'
  ];
  modalObservacionVisible = false;
  observacionForm: FormGroup;
  ventaSeleccionada: any = null;
  guardandoObservacion = false;

  modalEstadoVisible = false;
  estadoForm: FormGroup;
  estados: string[] = [
    'AB',
    'CANCELADO',
    'VALIDADA',
    'CIERRE PARCIAL',
    'CIERRE COMPLETO',
    'DESCONECTADO',
    'PDT CANCELAR',
    'FALLIDA',
    'REHACER VENTA',
    'RECLAMADO AJUSTE',
    'PDT CANAL',
    'FV',
    'IRRESOLUBLE',
    'PDT SUBIR',
    'PDT VALIDAR',
    'NO LOCALIZADO',
    'NO CARGADA',
    'OK',
    'DENEGADO',
    'TRAMITADA OK',
    'PENDIENTE DE FILTRO'
  ];
  guardandoEstado = false;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  observaciones: any[] = [];
  nuevaObservacionForm: FormGroup;

  editForm: FormGroup;
  editModalVisible = false;
  selectedVenta: any = null;
  direcciones: Direccion[] = [];
  isFilterExpanded = false;

  backofficesSeguimiento: Backoffice[] = [];
  backofficesTramitador: Backoffice[] = [];
  modalBackofficeVisible = false;
  backofficeForm: FormGroup;
  ventasPendientes: number = 0;
  coordinadores : any[] = [];
  private subscriptions: Subscription[] = [];

  // Estadísticas de ventas
  totalVentas: number = 0;
  ventasPorSede: { [sede: string]: number } = {};
  ventasPorSedeYEstado: { [sede: string]: { [estado: string]: number } } = {};

  // Modal de guía de ventas
  modalGuiaVisible = false;
  videoGuiaUrl = '';

  // Método para usar Object.keys en el template
  Object = Object;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private ventaService: VentaService,
    private store: Store<fromRoot.State>,
    private generalService: GeneralService,
    private direccionService: DireccionService,
    private snackBar: MatSnackBar,
    private sedeService: SedeService,
    private logService: LogService,

  ) {
    // Obtener fecha de hoy y mañana
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    // Formatear fechas a YYYY-MM-DD
    const fechaInicio = today.toISOString().split('T')[0];
    const fechaFin = tomorrow.toISOString().split('T')[0];

    this.filterForm = this.fb.group({
      documento: [''],
      fechaInicio: [fechaInicio],
      fechaFin: [fechaFin],
      segmento: [''],
      estado: [''],
      coordinadorFiltro: [''],
      comercial: [''],
      dni: [''],
      movil: [''],
      fijo: [''],
      codigoVenta: [''],
      sede: [''],
      tipoVenta: ['']
    });

    this.observacionForm = this.fb.group({
      observacionEstado: ['', Validators.required]
    });
    this.estadoForm = this.fb.group({
      estado: ['', Validators.required]
    });
    this.nuevaObservacionForm = this.fb.group({
      observacion: ['', Validators.required]
    });

    this.editForm = this.fb.group({
      codigoVenta: [{value: '', disabled: true}],
      campania: [''],
      nombres_apellidos: [''],
      nif_nie: [''],
      nacionalidad: [''],
      nacimiento: [''],
      genero: [''],
      direccion: [''],
      cuenta_bancaria: [''],
      Coordinador: [''],
      operador: [''],
      segmento: [''],
      email: [''],
      permanencia: [''],
      tipoFibra: [''],
      horaInstalacion: [''],
      promocion: [''],
      tvDeco: [''],
       operacion: [''],
       movilContacto: [''],
      movilAPortar1: [''],
      movilAPortar2: [''],
      movilAPortar3: [''],
      movilAPortar4: [''],
      movilAPortar5: [''],
      movilAPortar6: [''],
      movilAPortar7: [''],
      movilAPortar8: [''],
      movilAPortar9: [''],
      movilAPortar10: [''],
      GBmovilAPortar1: [''],
      GBmovilAPortar2: [''],
      GBmovilAPortar3: [''],
      GBmovilAPortar4: [''],
      GBmovilAPortar5: [''],
      GBmovilAPortar6: [''],
      GBmovilAPortar7: [''],
      GBmovilAPortar8: [''],
      GBmovilAPortar9: [''],
      GBmovilAPortar10: [''],

      precioPromocionTiempo: [''],
      precioReal: [''],
      presentacionCCliente: [''],
      telefonoCompania: [''],
      documento: [''],
      nombresApellidosRL: [''],
      nif_nieRL: [''],
      usuarioSolivesa: [''],
      usuarioRetailX: [''],
      tipoVenta: [''],
       direcciones: this.fb.array([]) // Agregar el FormArray aquí
    });

    this.backofficeForm = this.fb.group({
      backoffice_seguimiento: [null],
      backoffice_tramitador: [null]
    });
  }

  /**
   * Verifica si el tema oscuro está activo
   */
  checkDarkTheme(): void {
    // Verificar si el body tiene la clase dark-theme
    this.isDarkTheme = document.body.classList.contains('dark-theme');
    console.log('Tema oscuro en ventas:', this.isDarkTheme);
  }

  /**
   * Observa cambios en el tema
   */
  observeThemeChanges(): void {
    // Crear un observer para detectar cambios en el body
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          this.checkDarkTheme();
        }
      });
    });

    // Iniciar la observación del body
    observer.observe(document.body, { attributes: true });
  }

  ngOnInit(): void {
    // Verificar el tema actual
    this.checkDarkTheme();

    // Observar cambios en el tema
    this.observeThemeChanges();
    this.obtenerCoordinadores();
    this.loadSedes();
    const usuario = this.generalService.usuario$;

    if (usuario) {
      this.roleUser = usuario.role || '';
      this.idUsuario = usuario.id || null;
      // Llamar a obtenerVentas con los filtros iniciales
      this.obtenerVentas(this.idUsuario, this.filterForm.value);

    } else {
      const userFromLocalStorage = localStorage.getItem('user');
      if (userFromLocalStorage) {
        const user = JSON.parse(userFromLocalStorage);
        this.roleUser = user.role || '';
        this.idUsuario = user.id || null;
        // Llamar a obtenerVentas con los filtros iniciales
        this.obtenerVentas(this.idUsuario, this.filterForm.value);

      } else {
        console.warn('No se encontró información del usuario.');
      }
    }

    // Configurar las columnas según el rol
    this.displayedColumns = this.isBackOffice() ?
      [  'documento', 'dni', 'agente', 'coordinador','sede', 'fecha_hora', 'acciones'] :
      [  'documento', 'dni', 'agente', 'coordinador','sede', 'fecha_hora', 'acciones'];

    if (this.isBACKOFFICETRAMITADOR()) {
      this.obtenerVentasPendientes();
    }
  }
  aplicarFiltros(): void {
    const filtros = this.filterForm.value;
    this.obtenerVentas(this.idUsuario, filtros);
  }

  formatDateForApi(date: any): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }

  obtenerVentas(idUsuario: number | null | any, filtros?: any): void {
    const baseUrl = 'https://apisozarusac.com/ventas/api/ventas/';
    let url = this.isAdmin() || this.isBackOffice() || this.isBACKOFFICETRAMITADOR()? baseUrl :this.isCoordinador() ? `${baseUrl}/coordinador/${idUsuario}/` :this.isBACKOFFICESEGUIMIENTO() ? `${baseUrl}/backofficeTramitador/${idUsuario}/` : `${baseUrl}/usuario/${idUsuario}/`;

    // Construir los parámetros de consulta
    const params = new URLSearchParams();

    if (filtros?.fechaInicio && filtros?.fechaFin) {
      params.append('fecha_inicio', filtros.fechaInicio);
      params.append('fecha_fin', filtros.fechaFin );
    }

    if (filtros?.documento) {
      params.append('documento', filtros.documento);
    }
    if (filtros?.codigoVenta) {
      params.append('codigoVenta', filtros.codigoVenta);
    }
    if (filtros?.coordinadorFiltro) {
      params.append('coordinador', filtros.coordinadorFiltro);
    }
    // Agregar los nuevos filtros
    if (filtros?.segmento) {
      params.append('segmento', filtros.segmento);
    }

    if (filtros?.estado) {
      params.append('estado', filtros.estado);
    }

    if (filtros?.comercial) {
      params.append('comercial', filtros.comercial);
    }

    // Agregar los filtros faltantes
    if (filtros?.dni) {
      params.append('dni', filtros.dni);
    }

    if (filtros?.movil) {
      params.append('movil', filtros.movil);
    }

    if (filtros?.fijo) {
      params.append('fijo', filtros.fijo);
    }
    if (filtros?.sede) {
      params.append('sede', filtros.sede);
    }
    if (filtros?.tipoVenta) {
      params.append('tipoVenta', filtros.tipoVenta);
    }

    // Agregar los parámetros a la URL si existen
    const queryString = params.toString();
    if (queryString) {
      url += `?${queryString}`;
    }

     this.http.get<any[]>(url).subscribe({
      next: (res) => {
        this.ventas.data = res;
        this.ventas.paginator = this.paginator;
        this.ventas.sort = this.sort;
        this.calcularEstadisticasVentas(res);
       },
      error: (error) => {
        console.error('Error al obtener ventas:', error);
       }
    });
  }
  ExportarExcel(idUsuario: number | null | any, filtros?: any): void {
    const baseUrl = 'https://apisozarusac.com/ventas/api/ventas/masivo/exportar-excel/';

    // Construir los parámetros de consulta
    const params = new URLSearchParams();

    // Agregar el ID de usuario si existe
    if(this.isCoordinador() || this.isBACKOFFICESEGUIMIENTO()) {
      params.append('id_usuario', idUsuario.toString());
    }

    // Agregar los parámetros de tipo_reporte y fecha si vienen del modal
    if (filtros?.tipoReporte) {
      params.append('tipo_reporte', filtros.tipoReporte);
      if (filtros.fecha) {
        // Convertir la fecha al formato YYYY-MM-DD
        const fecha = new Date(filtros.fecha);
        const fechaStr = fecha.toISOString().split('T')[0];
        params.append('fecha', fechaStr);
      }
    }

    if (filtros?.fechaInicio && filtros?.fechaFin) {
      params.append('fecha_inicio', filtros.fechaInicio);
      params.append('fecha_fin', filtros.fechaFin);
    }

    if (filtros?.documento) {
      params.append('documento', filtros.documento);
    }

    // Agregar los nuevos filtros
    if (filtros?.segmento) {
      params.append('segmento', filtros.segmento);
    }

    if (filtros?.estado) {
      params.append('estado', filtros.estado);
    }

    if (filtros?.coordinadorFiltro) {
      params.append('coordinador', filtros.coordinadorFiltro);
    }

    if (filtros?.comercial) {
      params.append('comercial', filtros.comercial);
    }

    // Agregar los filtros faltantes
    if (filtros?.dni) {
      params.append('dni', filtros.dni);
    }

    if (filtros?.movil) {
      params.append('movil', filtros.movil);
    }

    if (filtros?.fijo) {
      params.append('fijo', filtros.fijo);
    }
    if (filtros?.sede) {
      params.append('sede', filtros.sede);
    }
    if (filtros?.tipoVenta) {
      params.append('tipoVenta', filtros.tipoVenta);
    }

    // Agregar los parámetros a la URL
    const queryString = params.toString();
    const url = `${baseUrl}${queryString ? '?' + queryString : ''}`;

    console.log('URL de exportación:', url); // Para depuración

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        // Verificar si el blob es un JSON de error (puede ocurrir con respuestas de error)
        if (blob.type === 'application/json') {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              console.error('Error del servidor:', errorJson);
              Swal.fire('Error', errorJson.detail || 'Error al generar el Excel', 'error');
            } catch (e) {
              Swal.fire('Error', 'No se pudo procesar la respuesta del servidor', 'error');
            }
          };
          reader.readAsText(blob);
          return;
        }

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Nombre del archivo basado en el tipo de reporte
        let fileName = 'ReporteGeneral(';
        if (filtros?.tipoReporte === 'dia' && filtros?.fecha) {
          const fecha = new Date(filtros.fecha);
          fileName += `Dia_${fecha.toLocaleDateString('es-ES').replace(/\//g, '-')}`;
        } else if (filtros?.tipoReporte === 'mes' && filtros?.fecha) {
          const fecha = new Date(filtros.fecha);
          fileName += `Mes_${fecha.getMonth()+1}-${fecha.getFullYear()}`;
        } else {
          fileName += new Date().toISOString().split('T')[0];
        }

        link.download = `${fileName}).xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error al descargar Excel:', error);

        // Intentar leer el cuerpo del error para mostrar un mensaje más específico
        if (error.error instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              Swal.fire('Error', errorJson.detail || 'No se pudo descargar el Excel', 'error');
            } catch (e) {
              Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
            }
          };
          reader.readAsText(error.error);
        } else {
          Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
        }
      }
    });
}
  onSearch(event: any): void {
    const value = this.filterForm.get('searchTerm')?.value.trim().toLowerCase() || '';
    this.ventas.filter = value;
  }
  crearNuevaVenta(): void {
    console.log('Crear nueva venta');
  }

  verDetalle(venta: any): void {
    console.log('verDetalle - Código de venta:', venta.codigoVenta);
    // Obtener los datos actualizados de la API
    this.ventaService.obtenerVentaPorId(venta.codigoVenta).subscribe({
      next: (response) => {
        console.log('verDetalle - Respuesta de API:', response);
        if (response && response.data) {
          this.ventaDetalle = response.data;
          console.log('verDetalle - Datos asignados desde API:', this.ventaDetalle);
        } else {
          // Fallback a datos originales si la API no devuelve datos
          console.log('verDetalle - Usando datos originales como fallback');
          this.ventaDetalle = venta;
        }
        this.modalVisible = true;
      },
      error: (error) => {
        console.error('Error al obtener detalle de venta:', error);
        // Fallback a datos originales en caso de error
        console.log('verDetalle - Error en API, usando datos originales');
        this.ventaDetalle = venta;
        this.modalVisible = true;
        // Mostrar warning en lugar de error para no bloquear al usuario
        Swal.fire('Advertencia', 'Se muestran los datos en cache. Algunos datos podrían no estar actualizados.', 'warning');
      }
    });
  }

  closeModal(): void {
    this.modalVisible = false;
    this.ventaDetalle = null;
  }
  downloadExcelDetalle(codigoVenta: string): void {
    const url = `${environment.urlVentas}ventas/${codigoVenta}/excel/`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Venta-${codigoVenta}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error al descargar Excel:', error);
        Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
      }
    });
  }
    // Cargar todas las sedes activas
    loadSedes(): void {
      this.loading = true;

      this.subscriptions.push(
        this.sedeService.getAllSedes().subscribe({
          next: (response) => {
            if (response.rpta === 1 && response.data) {
              this.sedes = response.data.filter(sede => sede.activo);
            } else {
              this.sedes = [];
              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'No se pudieron cargar las sedes. ' + (response.msg || '')
              });
            }
            this.loading = false;
          },
          error: (error) => {
            console.error('Error al cargar sedes:', error);
            this.loading = false;
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'No se pudieron cargar las sedes. Por favor, intente nuevamente.'
            });
          }
        })
      );
    }
  downloadExcelDetalleEmpresa(codigoVenta: string): void {
    const url = `${environment.urlVentas}ventas/empresa/${codigoVenta}/excel/`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Venta-${codigoVenta}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error al descargar Excel:', error);
        Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
      }
    });
  }
  downloadOrPrint(elementId: string): void {
    const element = document.getElementById(elementId);
    if (!element) return;

    // Configuración para html2canvas
    const options = {
      scale: 2, // Mejor calidad
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight
    };

    html2canvas(element, options).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
      pdf.save(`Venta-${this.ventaDetalle.codigoVenta || 'detalle'}.pdf`);
    });
  }

  eliminarVenta(codigo: string,idVenta:number): void {
    Swal.fire({
      title: '¿Estás seguro?',
      text: "Esta acción no se puede revertir",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        this.ventaService.eliminarVenta(codigo,idVenta).subscribe({
          next: (response) => {
            this.logService.logEliminacion(
              'Ventas',
              'Ventas',
              idVenta,
              JSON.stringify(codigo)
            ).subscribe();
            Swal.fire(
              '¡Eliminado!',
              'La venta ha sido eliminada.',
              'success'
            );
            // Actualizar la tabla
            this.obtenerVentas(this.idUsuario, this.filterForm.value);
          },
          error: (error) => {
            console.error('Error al eliminar:', error);
            Swal.fire(
              'Error',
              'No se pudo eliminar la venta',
              'error'
            );
          }
        });
      }
    });
  }

  canEditVenta(venta: any): boolean {
    // Obtener el ID del usuario actual
    const userFromLocalStorage = localStorage.getItem('user') || '';
    const user = JSON.parse(userFromLocalStorage);

    const currentUserId = user.id.toString();

    // Verificar si el usuario actual es el creador de la venta
    return venta.idUsuario?.toString() === currentUserId;
  }

  editarVenta(venta: any): void {
    console.log('editarVenta - Código de venta:', venta.codigoVenta);
    // Obtener los datos actualizados de la API
    this.ventaService.obtenerVentaPorId(venta.codigoVenta).subscribe({
      next: (response) => {
        console.log('editarVenta - Respuesta de API:', response);
        const ventaActualizada = response.data as any;
        console.log('editarVenta - Datos de venta actualizados:', ventaActualizada);
        this.selectedVenta = ventaActualizada;
        this.tipoCliente = ventaActualizada.segmento || '';

        this.editForm.patchValue({
          codigoVenta: ventaActualizada.codigoVenta,
          campania: ventaActualizada.campania,
          nombres_apellidos: ventaActualizada.nombres_apellidos,
          nif_nie: ventaActualizada.nif_nie,
          nacionalidad: ventaActualizada.nacionalidad,
          nacimiento: ventaActualizada.nacimiento ? new Date(ventaActualizada.nacimiento) : null,
          genero: ventaActualizada.genero,
          direccion: ventaActualizada.direccion,
          cuenta_bancaria: ventaActualizada.cuenta_bancaria,
          Coordinador: ventaActualizada.Coordinador,
          operador: ventaActualizada.operador,
          segmento: ventaActualizada.segmento || '',
          email: ventaActualizada.email,
          permanencia: ventaActualizada.permanencia,
          tipoFibra: ventaActualizada.tipoFibra,
          horaInstalacion: ventaActualizada.horaInstalacion,
          promocion: ventaActualizada.promocion,
          tvDeco: ventaActualizada.tvDeco,
          operacion: ventaActualizada.operacion,
          movilContacto: ventaActualizada.movilContacto,
          movilAPortar1: ventaActualizada.movilAPortar1,
          movilAPortar2: ventaActualizada.movilAPortar2,
          movilAPortar3: ventaActualizada.movilAPortar3,
          movilAPortar4: ventaActualizada.movilAPortar4,
          movilAPortar5: ventaActualizada.movilAPortar5,
          movilAPortar6: ventaActualizada.movilAPortar6,
          movilAPortar7: ventaActualizada.movilAPortar7,
          movilAPortar8: ventaActualizada.movilAPortar8,
          movilAPortar9: ventaActualizada.movilAPortar9,
          movilAPortar10: ventaActualizada.movilAPortar10,
          GBmovilAPortar1: ventaActualizada.GBmovilAPortar1,
          GBmovilAPortar2: ventaActualizada.GBmovilAPortar2,
          GBmovilAPortar3: ventaActualizada.GBmovilAPortar3,
          GBmovilAPortar4: ventaActualizada.GBmovilAPortar4,
          GBmovilAPortar5: ventaActualizada.GBmovilAPortar5,
          GBmovilAPortar6: ventaActualizada.GBmovilAPortar6,
          GBmovilAPortar7: ventaActualizada.GBmovilAPortar7,
          GBmovilAPortar8: ventaActualizada.GBmovilAPortar8,
          GBmovilAPortar9: ventaActualizada.GBmovilAPortar9,
          GBmovilAPortar10: ventaActualizada.GBmovilAPortar10,
          precioPromocionTiempo: ventaActualizada.precioPromocionTiempo,
          precioReal: ventaActualizada.precioReal,
          presentacionCCliente: ventaActualizada.presentacionCCliente,
          telefonoCompania: ventaActualizada.telefonoCompania,
          nombresApellidosRL: ventaActualizada.nombresApellidosRL,
          nif_nieRL: ventaActualizada.nif_nieRL,
          usuarioRetailX: ventaActualizada.usuarioRetailX,
          usuarioSolivesa: ventaActualizada.usuarioSolivesa,
          tipoVenta: ventaActualizada.tipoVenta
        });

        // Verificar el tema actual
        this.checkDarkTheme();

        // Cargar direcciones
        this.direccionService.obtenerDireccionesPorVenta(ventaActualizada.codigoVenta)
          .subscribe({
            next: (direcciones: Direccion[]) => {
              console.log('Direcciones obtenidas:', direcciones);
              this.direcciones = direcciones;
              const direccionesFormArray = this.editForm.get('direcciones') as FormArray;
              direccionesFormArray.clear();

              if (direcciones && direcciones.length > 0) {
                direcciones.forEach((direccion: Direccion) => {
                  direccionesFormArray.push(this.fb.control(direccion.direccion));
                });
              } else {
                // Si no hay direcciones, agregamos al menos una vacía
                direccionesFormArray.push(this.fb.control(''));
              }

              // Mostrar el modal después de cargar todo
              this.editModalVisible = true;

              // Asegurarse de que el modal tenga el tema correcto
              setTimeout(() => {
                const modalElement = document.querySelector('.azulino-modal');
                if (modalElement && document.body.classList.contains('dark-theme')) {
                  modalElement.classList.add('dark-theme');
                  console.log('Aplicando tema oscuro al modal de editar venta');
                }
              }, 100);
            },
            error: (error: any) => {
              console.error('Error al cargar direcciones:', error);
              Swal.fire('Error', 'No se pudieron cargar las direcciones', 'error');
              // Agregamos una dirección vacía en caso de error
              const direccionesFormArray = this.editForm.get('direcciones') as FormArray;
              direccionesFormArray.clear();
              direccionesFormArray.push(this.fb.control(''));

              // Mostrar el modal incluso si hay error con direcciones
              this.editModalVisible = true;
            }
          });
      },
      error: (error) => {
        console.error('Error al obtener datos de venta para edición:', error);
        // Fallback a datos originales en caso de error
        console.log('editarVenta - Error en API, usando datos originales');
        this.selectedVenta = venta;
        this.tipoCliente = venta.segmento || '';

        this.editForm.patchValue({
          codigoVenta: venta.codigoVenta,
          campania: venta.campania,
          nombres_apellidos: venta.nombres_apellidos,
          nif_nie: venta.nif_nie,
          nacionalidad: venta.nacionalidad,
          nacimiento: venta.nacimiento ? new Date(venta.nacimiento) : null,
          genero: venta.genero,
          direccion: venta.direccion,
          cuenta_bancaria: venta.cuenta_bancaria,
          Coordinador: venta.Coordinador,
          operador: venta.operador,
          segmento: venta.segmento,
          email: venta.email,
          permanencia: venta.permanencia,
          tipoFibra: venta.tipoFibra,
          horaInstalacion: venta.horaInstalacion,
          promocion: venta.promocion,
          tvDeco: venta.tvDeco,
          operacion: venta.operacion,
          movilContacto: venta.movilContacto,
          movilAPortar1: venta.movilAPortar1,
          movilAPortar2: venta.movilAPortar2,
          movilAPortar3: venta.movilAPortar3,
          movilAPortar4: venta.movilAPortar4,
          movilAPortar5: venta.movilAPortar5,
          movilAPortar6: venta.movilAPortar6,
          movilAPortar7: venta.movilAPortar7,
          movilAPortar8: venta.movilAPortar8,
          movilAPortar9: venta.movilAPortar9,
          movilAPortar10: venta.movilAPortar10,
          GBmovilAPortar1: venta.GBmovilAPortar1,
          GBmovilAPortar2: venta.GBmovilAPortar2,
          GBmovilAPortar3: venta.GBmovilAPortar3,
          GBmovilAPortar4: venta.GBmovilAPortar4,
          GBmovilAPortar5: venta.GBmovilAPortar5,
          GBmovilAPortar6: venta.GBmovilAPortar6,
          GBmovilAPortar7: venta.GBmovilAPortar7,
          GBmovilAPortar8: venta.GBmovilAPortar8,
          GBmovilAPortar9: venta.GBmovilAPortar9,
          GBmovilAPortar10: venta.GBmovilAPortar10,
          precioPromocionTiempo: venta.precioPromocionTiempo,
          precioReal: venta.precioReal,
          presentacionCCliente: venta.presentacionCCliente,
          telefonoCompania: venta.telefonoCompania,
          nombresApellidosRL: venta.nombresApellidosRL,
          nif_nieRL: venta.nif_nieRL,
          usuarioRetailX: venta.usuarioRetailX,
          usuarioSolivesa: venta.usuarioSolivesa,
          tipoVenta: venta.tipoVenta
        });

        // Verificar el tema actual
        this.checkDarkTheme();

        // Cargar direcciones con datos originales
        this.direccionService.obtenerDireccionesPorVenta(venta.codigoVenta)
          .subscribe({
            next: (direcciones: Direccion[]) => {
              console.log('Direcciones obtenidas (fallback):', direcciones);
              this.direcciones = direcciones;
              const direccionesFormArray = this.editForm.get('direcciones') as FormArray;
              direccionesFormArray.clear();

              if (direcciones && direcciones.length > 0) {
                direcciones.forEach((direccion: Direccion) => {
                  direccionesFormArray.push(this.fb.control(direccion.direccion));
                });
              } else {
                direccionesFormArray.push(this.fb.control(''));
              }

              this.editModalVisible = true;

              setTimeout(() => {
                const modalElement = document.querySelector('.azulino-modal');
                if (modalElement && document.body.classList.contains('dark-theme')) {
                  modalElement.classList.add('dark-theme');
                  console.log('Aplicando tema oscuro al modal de editar venta');
                }
              }, 100);
            },
            error: (error: any) => {
              console.error('Error al cargar direcciones (fallback):', error);
              const direccionesFormArray = this.editForm.get('direcciones') as FormArray;
              direccionesFormArray.clear();
              direccionesFormArray.push(this.fb.control(''));
              this.editModalVisible = true;
            }
          });

        // Mostrar warning
        Swal.fire('Advertencia', 'Se muestran los datos en cache. Algunos datos podrían no estar actualizados.', 'warning');
      }
    });
  }

  guardarCambios(): void {
    if (!this.editForm.valid) return;

    const ventaData = this.editForm.getRawValue();
    const direcciones: string[] = this.direccionesFormArray.value.filter((dir: string) => dir.trim());

    // Primero actualizamos la venta
    this.ventaService.editarVenta(this.selectedVenta.codigoVenta, ventaData).subscribe({
      next: (response) => {
        this.logService.logEdicion(
          'Ventas',
          'Ventas',
          this.selectedVenta.id,
          JSON.stringify(this.selectedVenta),
          JSON.stringify(ventaData)
        ).subscribe();
        // Primero eliminamos todas las direcciones existentes
        this.direccionService.eliminarDireccionesPorVenta(this.selectedVenta.codigoVenta).subscribe({
          next: () => {
            // Si no hay nuevas direcciones para agregar
            if (direcciones.length === 0) {
              Swal.fire(
                '¡Actualizado!',
                'La venta ha sido actualizada correctamente.',
                'success'
              );

              this.cerrarModalEdicion();
              this.obtenerVentas(this.idUsuario, this.filterForm.value);
              return;
            }

            // Creamos las nuevas direcciones
            const direccionesPromises = direcciones.map((direccion: string) => {
              return this.direccionService.crearDireccion({
                codigoVenta: this.selectedVenta.codigoVenta,
                direccion: direccion
              }).toPromise();
            });

            Promise.all(direccionesPromises)
              .then(() => {
                Swal.fire(
                  '¡Actualizado!',
                  'La venta y direcciones han sido actualizadas correctamente.',
                  'success'
                );
                this.cerrarModalEdicion();

                this.obtenerVentas(this.idUsuario, this.filterForm.value);
              })
              .catch(error => {
                console.error('Error al crear nuevas direcciones:', error);
                Swal.fire(
                  'Advertencia',
                  'Se actualizó la venta pero hubo un error al actualizar algunas direcciones',
                  'warning'
                );
                this.cerrarModalEdicion();
                this.obtenerVentas(this.idUsuario, this.filterForm.value);
              });
          },
          error: (error) => {
            console.error('Error al eliminar direcciones existentes:', error);
            Swal.fire(
              'Error',
              'Hubo un error al actualizar las direcciones',
              'error'
            );
          }
        });
      },
      error: (error) => {
        console.error('Error al actualizar venta:', error);
        Swal.fire(
          'Error',
          'No se pudo actualizar la venta',
          'error'
        );
      }
    });
  }

  cerrarModalEdicion(): void {
    this.editModalVisible = false;
    this.selectedVenta = null;
    this.editForm.reset();
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  isAdmin(): boolean {
    return this.roleUser === 'ADMIN' || this.roleUser === 'PROGRAMADOR';
  }
  isBackOffice(): boolean {
    return this.roleUser === 'BACKOFFICE';
  }
  isCoordinador(): boolean {
    return this.roleUser === 'COORDINADOR';
  }
  isBACKOFFICETRAMITADOR(): boolean {
    return this.roleUser === 'BACKOFFICETRAMITADOR'  ;
  }
  isBACKOFFICESEGUIMIENTO(): boolean {
    return   this.roleUser === 'BACKOFFICESEGUIMIENTO';
  }
  isAsesor(): boolean {
    return this.roleUser === 'ASESOR';
  }
  abrirModalObservacion(venta: any): void {
    this.ventaSeleccionada = venta;
    this.modalObservacionVisible = true;
    this.cargarObservaciones(venta.codigoVenta);
  }

  cargarObservaciones(codigoVenta: string): void {
    this.ventaService.obtenerObservaciones(codigoVenta).subscribe({
      next: (observaciones) => {
        this.observaciones = observaciones;
      },
      error: (error) => {
        console.error('Error al cargar observaciones:', error);
        Swal.fire('Error', 'No se pudieron cargar las observaciones', 'error');
      }
    });
  }

  cerrarModalObservacion(): void {
    this.modalObservacionVisible = false;
    this.ventaSeleccionada = null;
    this.observacionForm.reset();
  }

  guardarNuevaObservacion(): void {
    if (this.nuevaObservacionForm.invalid || !this.ventaSeleccionada || this.guardandoObservacion) {
      return;
    }
    const userFromLocalStorage = localStorage.getItem('user') || '';
    const user = JSON.parse(userFromLocalStorage);

    this.guardandoObservacion = true;
    const observacionData: Observacion = {
      codigoVenta: this.ventaSeleccionada.codigoVenta,
      codi_usuario: user.id?.toString() || '', // Convert to string
      observacion: this.nuevaObservacionForm.get('observacion')?.value
    };

    this.ventaService.crearObservacion(observacionData).subscribe({
      next: (response) => {
        this.guardandoObservacion = false;
        this.nuevaObservacionForm.reset();
        this.cargarObservaciones(this.ventaSeleccionada.codigoVenta);
        Swal.fire('¡Éxito!', 'Observación guardada correctamente', 'success');
      },
      error: (error) => {
        this.guardandoObservacion = false;
        console.error('Error al guardar la observación:', error);
        Swal.fire('Error', 'No se pudo guardar la observación', 'error');
      }
    });
  }
  abrirModalEstado(venta: any): void {
    this.ventaSeleccionada = venta;
    this.ventaService.obtenerVentaPorId(venta.codigoVenta).subscribe({
      next: (response) => {
         this.estadoForm.patchValue({
          estado: venta.Estado || ''
        });
        this.modalEstadoVisible = true;
      },
      error: (error) => {
        console.error('Error al cargar el estado:', error);
        Swal.fire('Error', 'No se pudo cargar el estado', 'error');
      }
    });
  }

  cerrarModalEstado(): void {
    this.modalEstadoVisible = false;
    this.ventaSeleccionada = null;
    this.estadoForm.reset();
  }

  guardarEstado(): void {
    if (this.estadoForm.invalid || !this.ventaSeleccionada || this.guardandoEstado) {
      return;
    }

    this.guardandoEstado = true;
    const estado = this.estadoForm.get('estado')?.value;

    this.ventaService.actualizarEstado(this.ventaSeleccionada.codigoVenta, estado)
      .subscribe({
        next: (response) => {
          this.guardandoEstado = false;
          Swal.fire('¡Éxito!', 'Estado actualizado correctamente', 'success');
          this.cerrarModalEstado();
          this.obtenerVentas(this.idUsuario);
        },
        error: (error) => {
          this.guardandoEstado = false;
          console.error('Error al guardar el estado:', error);
          Swal.fire('Error', 'No se pudo actualizar el estado', 'error');
        }
      });
  }
  formatearFecha(fecha: string): string {
    if (!fecha) return 'Sin fecha';

    const opciones: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',

    };

    return new Date(fecha).toLocaleDateString('es-ES', opciones);
  }
  formatearFechaHora(fecha: string): string {
    if (!fecha) return 'Sin fecha';

    const opciones: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };

    return new Date(fecha).toLocaleDateString('es-ES', opciones);
  }

  abrirModalEdicion(venta: any): void {
    this.selectedVenta = venta;

    // Primero cargamos los datos de la venta en el formulario
    this.editForm.patchValue({
      codigoVenta: venta.codigoVenta,
      campania: venta.campania,
      nombres_apellidos: venta.nombres_apellidos,
      nif_nie: venta.nif_nie,
      nacionalidad: venta.nacionalidad,
      nacimiento: venta.nacimiento,
      genero: venta.genero,
      direccion: venta.direccion,
      cuenta_bancaria: venta.cuenta_bancaria,
      Coordinador: venta.Coordinador,
      operador: venta.operador,
      segmento: venta.segmento,
      email: venta.email,
      permanencia: venta.permanencia,
      tipoFibra: venta.tipoFibra,
      horaInstalacion: venta.horaInstalacion,
      promocion: venta.promocion,
      tvDeco: venta.tvDeco,
       operacion: venta.operacion,
       movilContacto: venta.movilContacto,
      movilAPortar1: venta.movilAPortar1,
      movilAPortar2: venta.movilAPortar2,
      movilAPortar3: venta.movilAPortar3,
      movilAPortar4: venta.movilAPortar4,
      movilAPortar5: venta.movilAPortar5,
      movilAPortar6: venta.movilAPortar6,
      movilAPortar7: venta.movilAPortar7,
      movilAPortar8: venta.movilAPortar8,
      movilAPortar9: venta.movilAPortar9,
      movilAPortar10: venta.movilAPortar10,
      GBmovilAPortar1: venta.GBmovilAPortar1,
      GBmovilAPortar2: venta.GBmovilAPortar2,
      GBmovilAPortar3: venta.GBmovilAPortar3,
      GBmovilAPortar4: venta.GBmovilAPortar4,
      GBmovilAPortar5: venta.GBmovilAPortar5,
      GBmovilAPortar6: venta.GBmovilAPortar6,
      GBmovilAPortar7: venta.GBmovilAPortar7,
      GBmovilAPortar8: venta.GBmovilAPortar8,
      GBmovilAPortar9: venta.GBmovilAPortar9,
      GBmovilAPortar10: venta.GBmovilAPortar10,
      precioPromocionTiempo: venta.precioPromocionTiempo,
      precioReal: venta.precioReal,
      presentacionCCliente: venta.presentacionCCliente,
      documento: venta.documento,
      nombresApellidosRL: venta.nombresApellidosRL,
      nif_nieRL: venta.nif_nieRL,
      usuarioRetailX: venta.usuarioRetailX,
      usuarioSolivesa: venta.usuarioSolivesa,
      tipoVenta: venta.tipoVenta
    });

    // Luego cargamos las direcciones
    console.log('Cargando direcciones para venta:', venta.codigoVenta);

  }

  // Agregar getters para el FormArray
  get direccionesFormArray(): FormArray {
    return this.editForm.get('direcciones') as FormArray;
  }

  // Método para agregar dirección
  agregarDireccion(): void {
    this.direccionesFormArray.push(this.fb.control(''));
  }

  // Método para eliminar dirección
  eliminarDireccion(index: number): void {
    this.direccionesFormArray.removeAt(index);
  }
  parseDirecciones(direcciones: any): any[] {
    if (!direcciones) return [];
    try {
      return typeof direcciones === 'string' ? JSON.parse(direcciones) : direcciones;
    } catch (e) {
      console.error("Error al parsear direcciones", e);
      return [];
    }
  }

  downloadTableExcel(): void {
    // Preparar los datos del detalle de la venta
    const data = this.ventas.data.map(ventaDetalle => ({
      'Fecha de Registro': this.formatearFechaHora(ventaDetalle.fechaRegistro) || '',
      'Nombres y Apellidos': ventaDetalle.nombres_apellidos || '',
      'NIF/NIE': ventaDetalle.nif_nie || '',
      'Nacionalidad': ventaDetalle.nacionalidad || '',
      'Fecha de Nacimiento': ventaDetalle.nacimiento ? ventaDetalle.nacimiento : '',
      'Género': ventaDetalle.genero || '',
      'Correo Electrónico': ventaDetalle.email || '',
      'Cuenta Bancaria': ventaDetalle.cuenta_bancaria || '',
      'Permanencia': ventaDetalle.permanencia ? 'SÍ' : 'NO',
      'Dirección': ventaDetalle.direccion || '',
      'Tipo de Fibra': ventaDetalle.tipoFibra || '',
      'Hora de Instalación': ventaDetalle.horaInstalacion || '',
      'Promoción': ventaDetalle.promocion || '',
      'TV/Deco': ventaDetalle.tvDeco || '',
        'Móvil de Contacto': ventaDetalle.movilContacto || '',
      'Fijo': ventaDetalle.telefonoCompania || '',
      'Móvil a Portar 1/Compañía': ventaDetalle.movilAPortar1 || '',
      'Móvil a Portar 2/Compañía': ventaDetalle.movilAPortar2 || '',
      'Móvil a Portar 3/Compañía': ventaDetalle.movilAPortar3 || '',
      'Móvil a Portar 4/Compañía': ventaDetalle.movilAPortar4 || '',
      'Móvil a Portar 5/Compañía': ventaDetalle.movilAPortar5 || '',
      'Precio Promoción/Tiempo': ventaDetalle.precioPromocionTiempo || '',
      'Precio Real/Después de Promoción': ventaDetalle.precioReal || '',
      'Presentación con el Cliente': ventaDetalle.presentacionCCliente || '',
      'Comercial': `${ventaDetalle.apellido} ${ventaDetalle.nombre}` || '',
      'Coordinador': ventaDetalle.Coordinador || '',
      'Observación': ventaDetalle.operacion || '',
      'Operador': ventaDetalle.operador || '',
      'segmento': ventaDetalle.segmento || '',
      'Direcciones': this.parseDirecciones(ventaDetalle.direcciones).map(dir => dir.direccion).join(', ') || '',
    }));

    // Crear el libro de Excel
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const workbook: XLSX.WorkBook = { Sheets: { 'Venta Detalle': worksheet }, SheetNames: ['Venta Detalle'] };

    // Generar el archivo
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // Descargar el archivo
    const fileName = `VentaDetalle_${new Date().toISOString().split('T')[0]}.xlsx`;
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  toggleFilters(): void {
    this.isFilterExpanded = !this.isFilterExpanded;
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.aplicarFiltros();
  }
  recargarVentas(): void {
    this.obtenerVentas(this.idUsuario, this.filterForm.value);
  }
  abrirModalBackoffice(venta: any): void {
    this.ventaSeleccionada = venta;
    this.modalBackofficeVisible = true;

    // Primero obtener las listas de backoffices
    forkJoin([
      this.ventaService.obtenerBackoffices('SEGUIMIENTO'),
      this.ventaService.obtenerBackoffices('TRAMITADOR')
    ]).subscribe({
      next: ([seguimiento, tramitador]) => {
        this.backofficesSeguimiento = seguimiento;
        this.backofficesTramitador = tramitador;
        console.log('Backoffices:', this.backofficesSeguimiento, this.backofficesTramitador);

        // Luego obtener los backoffices asignados
        this.ventaService.obtenerBackofficesAsignados(venta.idVenta).subscribe({
          next: (asignados) => {
            const backSeguimiento = asignados.find(b => b.tipoBack === 'SEGUIMIENTO');
            const backTramitador = asignados.find(b => b.tipoBack === 'TRAMITADOR');

            this.backofficeForm.patchValue({
              backoffice_seguimiento: backSeguimiento?.idBackoffice || null,
              backoffice_tramitador: backTramitador?.idBackoffice || null
            });
          },
          error: (error) => {
            console.error('Error al cargar backoffices asignados:', error);
            // Solo mostramos el error en consola, pero permitimos que el modal se muestre
            // con los campos vacíos
            this.backofficeForm.patchValue({
              backoffice_seguimiento: null,
              backoffice_tramitador: null
            });
          }
        });
      },
      error: (error) => {
        console.error('Error al cargar las listas de backoffices:', error);
        Swal.fire('Error', 'No se pudieron cargar los datos de backoffices', 'error');
      }
    });
  }

  async asignarBackoffices(): Promise<void> {
    if (!this.ventaSeleccionada || this.backofficeForm.invalid) return;

    const backoffice_seguimiento = this.backofficeForm.get('backoffice_seguimiento')?.value;
    const backoffice_tramitador = this.backofficeForm.get('backoffice_tramitador')?.value;

    if (!backoffice_seguimiento && !backoffice_tramitador) {
      Swal.fire('Advertencia', 'Debe seleccionar al menos un backoffice', 'warning');
      return;
    }

    try {
      // Primero eliminar los backoffices existentes
      await this.ventaService.eliminarBackoffice(this.ventaSeleccionada.idVenta.toString()).toPromise();

      const asignaciones = [];

      if (backoffice_seguimiento) {
        asignaciones.push(
          this.ventaService.asignarBackoffice({
            idBackoffice: backoffice_seguimiento,
            idVenta: this.ventaSeleccionada.idVenta,
            tipoBack: 'SEGUIMIENTO'
          })
        );
      }

      if (backoffice_tramitador) {
        asignaciones.push(
          this.ventaService.asignarBackoffice({
            idBackoffice: backoffice_tramitador,
            idVenta: this.ventaSeleccionada.idVenta,
            tipoBack: 'TRAMITADOR'
          })
        );
      }

      // Ejecutar todas las asignaciones en paralelo después de la eliminación
      await forkJoin(asignaciones).toPromise();

      Swal.fire('¡Éxito!', 'Backoffices asignados correctamente', 'success');
      this.cerrarModalBackoffice();
      this.obtenerVentas(this.idUsuario, this.filterForm.value);
    } catch (error) {
      console.error('Error al asignar backoffices:', error);
      Swal.fire('Error', 'No se pudieron asignar los backoffices', 'error');
    }
  }

  cerrarModalBackoffice(): void {
    this.modalBackofficeVisible = false;
    this.ventaSeleccionada = null;
    this.backofficeForm.reset();
  }

  getNombreCompleto(backoffice: Backoffice): string {
    return `${backoffice.nombre} ${backoffice.apellido}`.trim();
  }

  private obtenerVentasPendientes(): void {
    const baseUrl = 'https://apisozarusac.com/ventas/api/ventas/';
    const url = `${baseUrl}/backofficeTramitador/${this.idUsuario}/`;

    const params = new URLSearchParams();
    params.append('estado', 'PENDIENTE DE FILTRO');

    this.http.get<any[]>(`${url}?${params}`).subscribe({
      next: (ventas) => {
        this.ventasPendientes = ventas.length;
      },
      error: (error) => {
        console.error('Error al obtener ventas pendientes:', error);
        this.ventasPendientes = 0;
      }
    });
  }

  private calcularEstadisticasVentas(ventas: any[]): void {
    this.totalVentas = ventas.length;
    this.ventasPorSede = {};
    this.ventasPorSedeYEstado = {};

    ventas.forEach(venta => {
      const sede = venta.ciudad || 'Sin sede';
      const estado = venta.Estado || 'Sin estado';

      // Contar total por sede
      if (this.ventasPorSede[sede]) {
        this.ventasPorSede[sede]++;
      } else {
        this.ventasPorSede[sede] = 1;
      }

      // Contar por sede y estado
      if (!this.ventasPorSedeYEstado[sede]) {
        this.ventasPorSedeYEstado[sede] = {};
      }

      if (this.ventasPorSedeYEstado[sede][estado]) {
        this.ventasPorSedeYEstado[sede][estado]++;
      } else {
        this.ventasPorSedeYEstado[sede][estado] = 1;
      }
    });
  }

  // Método helper para obtener las claves de un objeto (para usar en el template)
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  // Método para obtener estados ordenados por prioridad
  getEstadosOrdenados(sede: string): string[] {
    if (!this.ventasPorSedeYEstado[sede]) {
      return [];
    }

    const estados = Object.keys(this.ventasPorSedeYEstado[sede]);

    // Definir orden de prioridad para los estados
    const ordenPrioridad: { [estado: string]: number } = {
      'VALIDADA': 1,
      'CIERRE COMPLETO': 2,
      'CIERRE PARCIAL': 3,
      'PENDIENTE DE FILTRO': 4,
      'PDT VALIDAR': 5,
      'TRAMITADA OK': 6,
      'OK': 7,
      'FALLIDA': 8,
      'CANCELADO': 9,
      'DENEGADO': 10,
      'DESCONECTADO': 11,
      'NO LOCALIZADO': 12,
      'IRRESOLUBLE': 13
    };

    return estados.sort((a, b) => {
      const prioridadA = ordenPrioridad[a] || 999;
      const prioridadB = ordenPrioridad[b] || 999;
      return prioridadA - prioridadB;
    });
  }
  private obtenerCoordinadores(): void {
    const baseUrl = 'https://apisozarusac.com/ventas/api/coordinadores/listado/';
    const url = `${baseUrl}`;

    this.http.get<any>(`${url}`).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.coordinadores = response.data;
        } else {
          this.coordinadores = [];
          console.error('La respuesta no tiene el formato esperado:', response);
        }
      },
      error: (error) => {
        console.error('Error al obtener coordinadores:', error);
        this.coordinadores = [];
      }
    });
  }

  // ===== MÉTODOS PARA MODAL DE GUÍA =====

  /**
   * Abre el modal con la guía de creación de ventas
   */
  abrirGuiaCreacionVentas(): void {
    this.videoGuiaUrl = 'https://apisozarusac.com/ventas/api/descargar-guia/';
    this.modalGuiaVisible = true;
  }

  /**
   * Cierra el modal de la guía
   */
  cerrarModalGuia(): void {
    this.modalGuiaVisible = false;
    this.videoGuiaUrl = '';
  }



}

