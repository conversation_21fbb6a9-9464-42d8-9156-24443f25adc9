import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface GoogleDriveFile {
  id: string;
  name: string;
  size?: number;
  mimeType: string;
  createdTime: string;
  modifiedTime: string;
  parents?: string[];
  webViewLink?: string;
  webContentLink?: string;
  isFolder?: boolean;
}

export interface GoogleDriveResponse<T> {
  rpta: number;
  msg: string;
  data: T;
  nextPageToken?: string;
  totalItems?: number;
}

export interface CreateFolderRequest {
  folderName: string;
  parentFolderId?: string;
}

export interface UploadFileRequest {
  file: File;
  fileName?: string;
  folderId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GoogleDriveService {
  private readonly baseUrl = `${environment.url}api/google-drive`;

  constructor(private http: HttpClient) {}

  /**
   * Lista archivos y carpetas en Google Drive
   * @param folderId ID de la carpeta (opcional)
   * @param pageSize Número de elementos por página
   * @param searchQuery Término de búsqueda (opcional)
   * @param pageToken Token de página para paginación (opcional)
   */
  listFiles(folderId?: string, pageSize: number = 20, searchQuery?: string, pageToken?: string): Observable<GoogleDriveResponse<GoogleDriveFile[]>> {
    let params = new HttpParams().set('pageSize', pageSize.toString());

    if (folderId) {
      params = params.set('folderId', folderId);
    }

    if (searchQuery) {
      params = params.set('search', searchQuery);
    }

    if (pageToken) {
      params = params.set('pageToken', pageToken);
    }

    return this.http.get<GoogleDriveResponse<GoogleDriveFile[]>>(`${this.baseUrl}/files`, { params });
  }

  /**
   * Lista solo las carpetas en Google Drive
   * @param pageSize Número de carpetas por página
   * @param pageToken Token de página para paginación (opcional)
   */
  listFolders(pageSize: number = 50, pageToken?: string): Observable<GoogleDriveResponse<GoogleDriveFile[]>> {
    let params = new HttpParams().set('pageSize', pageSize.toString());

    if (pageToken) {
      params = params.set('pageToken', pageToken);
    }

    return this.http.get<GoogleDriveResponse<GoogleDriveFile[]>>(`${this.baseUrl}/folders`, { params });
  }

  /**
   * Crea una nueva carpeta en Google Drive
   * @param request Datos de la carpeta a crear
   */
  createFolder(request: CreateFolderRequest): Observable<GoogleDriveResponse<{folderId: string, folderName: string}>> {
    const formData = new FormData();
    formData.append('folderName', request.folderName);
    
    if (request.parentFolderId) {
      formData.append('parentFolderId', request.parentFolderId);
    }

    return this.http.post<GoogleDriveResponse<{folderId: string, folderName: string}>>(`${this.baseUrl}/folder`, formData);
  }

  /**
   * Sube un archivo a Google Drive
   * @param request Datos del archivo a subir
   */
  uploadFile(request: UploadFileRequest): Observable<GoogleDriveResponse<{fileId: string, fileName: string}>> {
    const formData = new FormData();
    formData.append('file', request.file);
    
    if (request.fileName) {
      formData.append('fileName', request.fileName);
    }
    
    if (request.folderId) {
      formData.append('folderId', request.folderId);
    }

    return this.http.post<GoogleDriveResponse<{fileId: string, fileName: string}>>(`${this.baseUrl}/upload`, formData);
  }

  /**
   * Elimina un archivo de Google Drive
   * @param fileId ID del archivo a eliminar
   */
  deleteFile(fileId: string): Observable<GoogleDriveResponse<string>> {
    return this.http.delete<GoogleDriveResponse<string>>(`${this.baseUrl}/${fileId}`);
  }

  /**
   * Obtiene información detallada de un archivo
   * @param fileId ID del archivo
   */
  getFileInfo(fileId: string): Observable<GoogleDriveResponse<GoogleDriveFile>> {
    return this.http.get<GoogleDriveResponse<GoogleDriveFile>>(`${this.baseUrl}/file/${fileId}`);
  }

  /**
   * Descarga un archivo de Google Drive
   * @param fileId ID del archivo a descargar
   */
  downloadFile(fileId: string): Observable<Blob> {
    return this.http.get(`${this.baseUrl}/download/${fileId}`, { responseType: 'blob' });
  }

  /**
   * Obtiene un enlace público para un archivo
   * @param fileId ID del archivo
   */
  getPublicLink(fileId: string): Observable<GoogleDriveResponse<{publicLink: string}>> {
    return this.http.post<GoogleDriveResponse<{publicLink: string}>>(`${this.baseUrl}/public-link/${fileId}`, {});
  }

  /**
   * Comparte un archivo con un usuario específico
   * @param fileId ID del archivo
   * @param email Email del usuario
   * @param role Rol del usuario (reader, writer, etc.)
   */
  shareFile(fileId: string, email: string, role: string = 'reader'): Observable<GoogleDriveResponse<{permissionId: string}>> {
    const body = { email, role };
    return this.http.post<GoogleDriveResponse<{permissionId: string}>>(`${this.baseUrl}/share/${fileId}`, body);
  }

  /**
   * Busca una carpeta por nombre
   * @param folderName Nombre de la carpeta
   */
  findFolderByName(folderName: string): Observable<GoogleDriveResponse<{folderId: string}>> {
    const params = new HttpParams().set('folderName', folderName);
    return this.http.get<GoogleDriveResponse<{folderId: string}>>(`${this.baseUrl}/find-folder`, { params });
  }

  /**
   * Obtiene el tamaño formateado de un archivo
   * @param bytes Tamaño en bytes
   */
  formatFileSize(bytes: number): string {
    if (!bytes) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Obtiene el icono apropiado para un tipo de archivo
   * @param mimeType Tipo MIME del archivo
   */
  getFileIcon(mimeType: string): string {
    // Validar que mimeType no sea null, undefined o vacío
    if (!mimeType || typeof mimeType !== 'string') {
      return 'insert_drive_file';
    }

    if (mimeType === 'application/vnd.google-apps.folder') {
      return 'folder';
    }

    if (mimeType.startsWith('image/')) {
      return 'image';
    }

    if (mimeType.startsWith('video/')) {
      return 'videocam';
    }

    if (mimeType.startsWith('audio/')) {
      return 'audiotrack';
    }

    if (mimeType.includes('pdf')) {
      return 'picture_as_pdf';
    }

    if (mimeType.includes('word') || mimeType.includes('document')) {
      return 'description';
    }

    if (mimeType.includes('sheet') || mimeType.includes('excel')) {
      return 'table_chart';
    }

    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) {
      return 'slideshow';
    }

    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('compressed')) {
      return 'archive';
    }

    return 'insert_drive_file';
  }

  /**
   * Verifica si un archivo es una carpeta
   * @param file Archivo de Google Drive
   */
  isFolder(file: GoogleDriveFile): boolean {
    return file && file.mimeType === 'application/vnd.google-apps.folder';
  }

  /**
   * Verifica si un tipo de archivo está permitido
   * @param file Archivo a verificar
   * @param allowedTypes Tipos permitidos (ej: ['audio/*', 'image/*'])
   */
  isFileTypeAllowed(file: File, allowedTypes: string[]): boolean {
    if (!allowedTypes || allowedTypes.length === 0) {
      return true;
    }

    return allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        const baseType = type.replace('/*', '');
        return file.type.startsWith(baseType);
      }
      return file.type === type;
    });
  }
}
