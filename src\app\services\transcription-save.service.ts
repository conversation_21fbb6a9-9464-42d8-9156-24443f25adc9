import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GoogleDriveService, CreateFolderRequest, UploadFileRequest } from './google-drive.service';
import { ClienteResidencial } from '@app/models/backend/clienteresidencial';
import { GenericResponse } from '@app/models/backend/generic-response';

export interface TranscriptionSaveRequest {
  numeroMovil: string;
  transcriptionText: string;
  callId: string;
  fileName?: string;
  forceOverwrite?: boolean; // Para permitir sobrescribir transcripción existente
}

export interface TranscriptionSaveResponse {
  success: boolean;
  message: string;
  cliente?: ClienteResidencial;
  fileUrl?: string;
  error?: string;
}

export interface TranscriptionVerificationResponse {
  tieneTranscripcion: boolean;
  clienteEncontrado: boolean;
  clienteId?: number;
  nombreCliente?: string;
  tieneTextoTranscripcion?: boolean;
  tieneUrlDriveTranscripcion?: boolean;
  urlDriveTranscripcion?: string;
  mensaje: string;
}

@Injectable({
  providedIn: 'root'
})
export class TranscriptionSaveService {
  private readonly baseUrl = environment.url;
  private readonly TEXTOS_TRANSCRITOS_FOLDER = 'TEXTOS_TRANSCRITOS';

  constructor(
    private http: HttpClient,
    private googleDriveService: GoogleDriveService
  ) {}

  /**
   * Verifica si un cliente ya tiene transcripción registrada
   */
  verificarTranscripcion(numeroMovil: string): Observable<TranscriptionVerificationResponse> {
    console.log('=== VERIFICANDO TRANSCRIPCIÓN ===');
    console.log('Número móvil:', numeroMovil);

    return this.http.get<GenericResponse<TranscriptionVerificationResponse>>(
      `${this.baseUrl}api/clientes/verificar-transcripcion/${numeroMovil}`
    ).pipe(
      map(response => {
        console.log('Respuesta del servidor:', response);
        if (response.rpta === 1) {
          return response.data;
        }
        throw new Error(response.msg || 'Error al verificar transcripción');
      }),
      catchError(error => {
        console.error('=== ERROR AL VERIFICAR TRANSCRIPCIÓN ===');
        console.error('Error:', error);
        return of({
          tieneTranscripcion: false,
          clienteEncontrado: false,
          mensaje: 'Error al verificar transcripción: ' + (error.message || error)
        } as TranscriptionVerificationResponse);
      })
    );
  }

  /**
   * Guarda la transcripción en el cliente residencial
   * 1. Busca el cliente por número móvil
   * 2. Verifica si ya tiene una transcripción
   * 3. Crea un archivo de texto con la transcripción
   * 4. Sube el archivo a Google Drive
   * 5. Actualiza el cliente con el texto y la URL
   */
  saveTranscription(request: TranscriptionSaveRequest): Observable<TranscriptionSaveResponse> {
    console.log('=== INICIANDO GUARDADO DE TRANSCRIPCIÓN ===');
    console.log('Request:', request);

    return this.buscarClientePorMovil(request.numeroMovil).pipe(
      switchMap(clientes => {
        if (!clientes || clientes.length === 0) {
          throw new Error(`No se encontró ningún cliente con el número móvil: ${request.numeroMovil}`);
        }

        const cliente = clientes[0]; // Tomar el primer cliente encontrado
        console.log('Cliente encontrado:', cliente);

        // Verificar si el cliente ya tiene una transcripción
        if (cliente.textoTranscription && cliente.textoTranscription.trim() !== '' && !request.forceOverwrite) {
          console.log('=== CLIENTE YA TIENE TRANSCRIPCIÓN ===');
          return of({
            success: false,
            message: 'Este cliente ya tiene una transcripción registrada',
            cliente: cliente,
            error: 'TRANSCRIPTION_ALREADY_EXISTS'
          } as TranscriptionSaveResponse);
        }

        // Crear el archivo de texto
        const fileName = request.fileName || this.generateFileName(request.callId, request.numeroMovil);
        const textFile = this.createTextFile(request.transcriptionText, fileName);

        // Subir archivo a Google Drive
        return this.uploadToGoogleDrive(textFile).pipe(
          switchMap(uploadResponse => {
            if (uploadResponse.rpta !== 1) {
              throw new Error(`Error al subir archivo: ${uploadResponse.msg}`);
            }

            const fileId = uploadResponse.data.fileId;
            const driveUrl = `https://drive.google.com/file/d/${fileId}/view`;

            // Actualizar cliente con la transcripción
            const clienteActualizado: ClienteResidencial = {
              ...cliente,
              textoTranscription: request.transcriptionText,
              urlDriveTranscripcion: driveUrl
            };

            return this.actualizarCliente(cliente.id, clienteActualizado).pipe(
              map(updateResponse => {
                if (updateResponse.rpta !== 1) {
                  throw new Error(`Error al actualizar cliente: ${updateResponse.msg}`);
                }

                console.log('=== TRANSCRIPCIÓN GUARDADA EXITOSAMENTE ===');
                return {
                  success: true,
                  message: 'Transcripción guardada exitosamente',
                  cliente: updateResponse.data,
                  fileUrl: driveUrl
                } as TranscriptionSaveResponse;
              })
            );
          })
        );
      }),
      catchError(error => {
        console.error('=== ERROR AL GUARDAR TRANSCRIPCIÓN ===');
        console.error('Error:', error);
        return of({
          success: false,
          message: 'Error al guardar la transcripción',
          error: error.message || error
        } as TranscriptionSaveResponse);
      })
    );
  }

  /**
   * Busca clientes por número móvil
   */
  private buscarClientePorMovil(numeroMovil: string): Observable<ClienteResidencial[]> {
    return this.http.get<GenericResponse<ClienteResidencial[]>>(
      `${this.baseUrl}api/clientes/buscar-por-movil/${numeroMovil}`
    ).pipe(
      map(response => {
        if (response.rpta === 1) {
          return response.data;
        }
        throw new Error(response.msg || 'Error al buscar cliente');
      })
    );
  }

  /**
   * Actualiza un cliente
   */
  private actualizarCliente(id: number, cliente: ClienteResidencial): Observable<GenericResponse<ClienteResidencial>> {
    return this.http.put<GenericResponse<ClienteResidencial>>(
      `${this.baseUrl}api/clientes/${id}`,
      cliente
    );
  }

  /**
   * Crea un archivo de texto con la transcripción
   */
  private createTextFile(transcriptionText: string, fileName: string): File {
    const content = this.formatTranscriptionContent(transcriptionText);
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    return new File([blob], fileName, { type: 'text/plain' });
  }

  /**
   * Formatea el contenido de la transcripción
   */
  private formatTranscriptionContent(transcriptionText: string): string {
    const timestamp = new Date().toLocaleString('es-ES');
    return `TRANSCRIPCIÓN DE AUDIO
========================

Fecha y hora: ${timestamp}
Generado por: Sistema CRM Midas

CONTENIDO:
----------
${transcriptionText}

========================
Fin de la transcripción`;
  }

  /**
   * Genera un nombre de archivo único
   */
  private generateFileName(callId: string, numeroMovil: string): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `transcripcion_${numeroMovil}_${callId}_${timestamp}.txt`;
  }

  /**
   * Sube el archivo a Google Drive en la carpeta TEXTOS_TRANSCRITOS
   */
  private uploadToGoogleDrive(file: File): Observable<any> {
    // Primero verificar si existe la carpeta TEXTOS_TRANSCRITOS
    return this.googleDriveService.listFolders().pipe(
      switchMap(foldersResponse => {
        let folderId: string | undefined;

        if (foldersResponse.rpta === 1) {
          const textosFolder = foldersResponse.data.find(
            folder => folder.name === this.TEXTOS_TRANSCRITOS_FOLDER
          );
          folderId = textosFolder?.id;
        }

        // Si no existe la carpeta, crearla
        if (!folderId) {
          const createFolderRequest: CreateFolderRequest = {
            folderName: this.TEXTOS_TRANSCRITOS_FOLDER
          };

          return this.googleDriveService.createFolder(createFolderRequest).pipe(
            switchMap(createResponse => {
              if (createResponse.rpta !== 1) {
                throw new Error(`Error al crear carpeta: ${createResponse.msg}`);
              }
              
              const newFolderId = createResponse.data.folderId;
              return this.uploadFileToFolder(file, newFolderId);
            })
          );
        } else {
          // La carpeta existe, subir directamente
          return this.uploadFileToFolder(file, folderId);
        }
      })
    );
  }

  /**
   * Sube el archivo a una carpeta específica
   */
  private uploadFileToFolder(file: File, folderId: string): Observable<any> {
    const uploadRequest: UploadFileRequest = {
      file: file,
      fileName: file.name,
      folderId: folderId
    };

    return this.googleDriveService.uploadFile(uploadRequest);
  }
}
