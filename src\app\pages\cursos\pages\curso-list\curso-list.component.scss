// ===== ESTILOS PARA CARDS DE CURSOS =====

.curso-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;

    &::before {
      opacity: 1;
    }
  }

  // Tema oscuro
  :host-context(.dark-theme) & {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid rgba(75, 85, 99, 0.3);

    &:hover {
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4) !important;
    }
  }
}

// ===== ESTILOS PARA EL DROPDOWN MENU =====

::ng-deep .curso-menu {
  .mat-mdc-menu-panel {
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important;
    min-width: 200px !important;
  }

  .mat-mdc-menu-item {
    height: 48px !important;
    padding: 0 16px !important;
    transition: all 0.2s ease !important;

    &:hover {
      background-color: rgba(59, 130, 246, 0.05) !important;
      transform: translateX(4px) !important;
    }

    .mat-icon {
      margin-right: 12px !important;
      font-size: 20px !important;
      width: 20px !important;
      height: 20px !important;
    }

    span {
      font-weight: 500 !important;
      font-size: 14px !important;
    }

    // Estilo especial para el botón de eliminar
    &.text-red-600 {
      &:hover {
        background-color: rgba(239, 68, 68, 0.05) !important;
        color: #dc2626 !important;
      }
    }
  }

  .mat-mdc-menu-divider {
    margin: 8px 0 !important;
    border-top-color: rgba(0, 0, 0, 0.08) !important;
  }

  // Tema oscuro para el menú
  :host-context(.dark-theme) & {
    .mat-mdc-menu-panel {
      background-color: #374151 !important;
      border-color: rgba(75, 85, 99, 0.3) !important;
    }

    .mat-mdc-menu-item {
      color: #f9fafb !important;

      &:hover {
        background-color: rgba(59, 130, 246, 0.1) !important;
      }

      &.text-red-600 {
        color: #fca5a5 !important;

        &:hover {
          background-color: rgba(239, 68, 68, 0.1) !important;
          color: #f87171 !important;
        }
      }
    }

    .mat-mdc-menu-divider {
      border-top-color: rgba(75, 85, 99, 0.3) !important;
    }
  }
}

// ===== ANIMACIONES PARA LOS ICONOS DE INFORMACIÓN =====

.curso-card {
  .mat-icon {
    transition: transform 0.2s ease;
  }

  &:hover {
    .mat-icon {
      transform: scale(1.1);
    }
  }
}

// ===== ESTILOS PARA LAS ETIQUETAS DE ESTADO =====

.curso-card {
  .status-badge {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }
  }
}

// ===== RESPONSIVE DESIGN =====

@media (max-width: 640px) {
  .curso-card {
    &:hover {
      transform: translateY(-4px) scale(1.01) !important;
    }
  }

  ::ng-deep .curso-menu {
    .mat-mdc-menu-panel {
      min-width: 180px !important;
    }

    .mat-mdc-menu-item {
      height: 44px !important;
      padding: 0 12px !important;

      .mat-icon {
        font-size: 18px !important;
        width: 18px !important;
        height: 18px !important;
      }

      span {
        font-size: 13px !important;
      }
    }
  }
}

// ===== MEJORAS DE ACCESIBILIDAD =====

.curso-card {
  &:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
}

::ng-deep .curso-menu {
  .mat-mdc-menu-item {
    &:focus {
      background-color: rgba(59, 130, 246, 0.1) !important;
      outline: none !important;
    }
  }
}

// ===== LOADING STATES =====

.curso-card {
  &.loading {
    pointer-events: none;
    opacity: 0.7;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: shimmer 1.5s infinite;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// ===== ASPECT RATIO PARA VIDEO E IMAGEN =====

.aspect-ratio-16-9 {
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}
