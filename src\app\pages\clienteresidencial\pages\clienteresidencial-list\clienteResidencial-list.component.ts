import {
  Component,
  Input,
  OnInit,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { Store, select } from '@ngrx/store';
import { FormBuilder, FormGroup } from '@angular/forms';
import * as fromClienteActions from '../../store/save/save.actions';
import * as fromClienteSelectors from '../../store/save/save.selectors';
import {
  ClienteConUsuarioDTO,
  ClienteResidencial,
} from '@app/models/backend/clienteresidencial';
import * as fromRoot from '@app/store';
import { Observable, take } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import * as fromUser from '@app/store/user';
import { MatDialog } from '@angular/material/dialog';
import { formatDate } from '@angular/common';
import { ExportByDateDialogComponent } from './export-by-date-dialog.component';
import { ExportByDateRangeDialogComponent } from './export-by-date-range-dialog.component';
import { GraficosSedeComponent } from '../../components/graficos-sede/graficos-sede.component';
import { skip } from 'rxjs/operators';
import { jsPDF } from 'jspdf';
import { User } from '@app/models/backend/user/index';
import html2canvas from 'html2canvas';
import Swal from 'sweetalert2';
import { Router } from '@angular/router';
import { GoogleDriveExplorerComponent } from '@app/shared/components/google-drive-explorer/google-drive-explorer.component';
import { TranscriptionDialogComponent, TranscriptionDialogData } from '@app/shared/components/transcription-dialog/transcription-dialog.component';

interface ClientePageResponse {
  clientes: ClienteConUsuarioDTO[];
  currentPage: number;
  totalItems: number;
  totalPages: number;
}

interface Filter {
  dniAsesor: string | null;
  nombreAsesor: string | null;
  numeroMovil: string | null;
  fecha: string | null;
}

@Component({
  selector: 'app-cliente-residencial-list',
  templateUrl: './clienteResidencial-list.component.html',
})
export class ClienteResidencialListComponent implements OnInit, AfterViewInit {
  @Input() user: User | null = null;
  @ViewChild('paginator') paginator!: MatPaginator;
  @ViewChild('graficosSedeComponent')
  graficosSedeComponent!: GraficosSedeComponent;

  clientesPage$!: Observable<ClientePageResponse>;
  selectedCliente$!: Observable<ClienteResidencial | null>;
  loading$!: Observable<boolean>;
  error$!: Observable<string | null>;
  user$!: Observable<fromUser.UserResponse>;
  // Variable para controlar el spinner
  exportLoading = false;

  currentPage = 0;
  pageSize = 10;
  selectedAdvisorName!: string;
  filterForm!: FormGroup;
  currentFilters: Filter | null = null;
  modalVisible = false;
  // Agrega estas propiedades al inicio de la clase
  editMode: boolean = false;
  editForm!: FormGroup;

  // Propiedades para la tabla de datos
  tableColumns = [
    { property: 'dni', name: 'DNI', type: 'text', sortable: true },
    { property: 'asesor', name: 'Nombre Asesor', type: 'text', sortable: true },
    {
      property: 'fechaIngresado',
      name: 'Fecha Ingresado',
      type: 'date',
      sortable: true,
    },
    {
      property: 'numeroMovil',
      name: 'Número Móvil',
      type: 'text',
      sortable: true,
    },
    {
      property: 'coordinador',
      name: 'Coordinador',
      type: 'text',
      sortable: true,
    },
    { property: 'accion', name: 'Acciones', type: 'custom' },
  ];

  // Propiedad para controlar el tema oscuro
  isDarkTheme = false;
  tableData: ClienteConUsuarioDTO[] = [];
  tableLoading = false;
  tableTotalItems = 0;

  constructor(
    private store: Store<fromRoot.State>,
    private fb: FormBuilder,
    private http: HttpClient,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.user$ = this.store.pipe(
      select(fromUser.getUser)
    ) as Observable<fromUser.UserResponse>;

    // Verificar el tema actual antes de la detección de cambios
    this.isDarkTheme = document.body.classList.contains('dark-theme');

    // Observar cambios en el tema
    this.observeThemeChanges();

    this.editForm = this.fb.group({
      id: [''],
      campania: [''],
      nombresApellidos: [''],
      nifNie: [''],
      nacionalidad: [''],
      fechaNacimiento: [''],
      genero: [''],
      correoElectronico: [''],
      cuentaBancaria: [''],
      permanencia: [''],
      direccion: [''],
      tipoTecnologia: [''],
      velocidad: [''],
      movilContacto: [''],
      fijoCompania: [''],
      planActual: [''],
      codigoPostal: [''],
      provincia: [''],
      distrito: [''],
      ciudad: [''],
      tipoPlan: [''],
      icc: [''],
      autorizaSeguros: [false],
      autorizaEnergias: [false],
      ventaRealizada: [false],
      deseaPromocionesLowi: [false],
      observacionEstado: [''],
      numeroMoviles: [''],
      tipoFibra: [''],
      movilesAPortar: [[]],
      observacion: [''],
      numeroAgente: [''],
      estadoLlamada: [''],
      titularDelServicio: [''],
      futbol: [''],
      nombres: [''],
      fechaCreacion: [''],
    });

    this.filterForm = this.fb.group({
      dniAsesor: [''],
      nombreAsesor: [''],
      numeroMovil: [''],
      fecha: [''],
    });

    // Configurar observables
    this.loading$ = this.store.pipe(select(fromClienteSelectors.getLoading));
    this.error$ = this.store.pipe(select(fromClienteSelectors.getError));
    this.clientesPage$ = this.store.pipe(
      select(fromClienteSelectors.getPaginatedClientes)
    );
    this.selectedCliente$ = this.store.pipe(
      select(fromClienteSelectors.getSelectedCliente)
    );

    // Carga inicial: se utiliza el endpoint que filtra por la fecha actual
    this.loadClientesPage(this.currentPage, this.pageSize);
  }

  ngAfterViewInit(): void {
    // Suscribirse a cambios en la página seleccionada
    this.clientesPage$.subscribe((page) => {
      if (page) {
        this.currentPage = page.currentPage;
        if (this.paginator) {
          this.paginator.pageIndex = page.currentPage;
        }

        // Actualizar datos para la tabla
        // Asegurarse de que todos los elementos sean instancias válidas de ClienteConUsuarioDTO
        this.tableData = page.clientes.map((cliente) => {
          // Si ya es una instancia válida, devolverla tal cual
          if (typeof cliente.getFechaCreacionFormatted === 'function') {
            return cliente;
          }
          // Si no, convertirla usando fromJson
          return ClienteConUsuarioDTO.fromJson(cliente);
        });
        this.tableTotalItems = page.totalItems;
      }
    });

    // Suscribirse a cambios en el estado de carga
    this.loading$.subscribe((loading) => {
      this.tableLoading = loading;
    });
  }

  // Método para manejar eventos de paginación
  handlePageEvent(event: { pageIndex: number; pageSize: number }): void {
    this.currentPage = event.pageIndex || 0;
    this.pageSize = event.pageSize || 10;
    this.loadClientesPage(this.currentPage, this.pageSize);
  }

  // Métodos para la tabla de datos
  onTableAdd(): void {
    // Implementar si es necesario
  }

  onTableEdit(cliente: any): void {
    // Asegurarse de que cliente sea una instancia válida de ClienteConUsuarioDTO
    if (cliente && typeof cliente === 'object') {
      // Si ya es una instancia válida, usarla directamente
      if (typeof cliente.getFechaCreacionFormatted === 'function') {
        this.openDetails(cliente);
      } else {
        // Si no, convertirla primero
        const clienteConvertido = ClienteConUsuarioDTO.fromJson(cliente);
        this.openDetails(clienteConvertido);
      }
    }
  }

  onTableDelete(cliente: any): void {
    // Asegurarse de que cliente sea una instancia válida de ClienteConUsuarioDTO
    if (cliente && typeof cliente === 'object') {
      // Si ya es una instancia válida, usarla directamente
      if (typeof cliente.getFechaCreacionFormatted === 'function') {
        // Implementar la lógica de eliminación si es necesario
      } else {
        // Si no, convertirla primero
        // Implementar la lógica de eliminación si es necesario
      }
    }
  }

  onTableRefresh(): void {
    this.loadClientesPage(this.currentPage, this.pageSize);
  }

  onTableSearch(searchTerm: any): void {
    // Implementar búsqueda en tiempo real
    const term = typeof searchTerm === 'string' ? searchTerm : '';
    if (term) {
      this.filterForm.patchValue({
        nombreAsesor: term,
      });
    } else {
      this.filterForm.patchValue({
        nombreAsesor: '',
      });
    }
    this.currentPage = 0;
    this.aplicarFiltros();
  }

  loadClientesPage(page: number, size: number): void {
    if (this.currentFilters) {
      this.store.dispatch(
        fromClienteActions.loadClientesFiltrados({
          dniAsesor: this.currentFilters.dniAsesor,
          nombreAsesor: this.currentFilters.nombreAsesor,
          numeroMovil: this.currentFilters.numeroMovil,
          fecha: this.currentFilters.fecha,
          page,
          size,
        })
      );
    } else {
      this.store.dispatch(fromClienteActions.loadClientes({ page, size }));
    }
  }

  nextPage(): void {
    this.currentPage++;
    this.loadClientesPage(this.currentPage, this.pageSize);
  }

  prevPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.loadClientesPage(this.currentPage, this.pageSize);
    }
  }

  goToFirstPage(): void {
    this.currentPage = 0;
    this.loadClientesPage(this.currentPage, this.pageSize);
  }

  goToLastPage(): void {
    this.clientesPage$.pipe(take(1)).subscribe((pageData) => {
      if (pageData.totalPages > 0) {
        this.currentPage = pageData.totalPages - 1;
        this.loadClientesPage(this.currentPage, this.pageSize);
      }
    });
  }

  aplicarFiltros(): void {
    const { dniAsesor, nombreAsesor, numeroMovil, fecha } =
      this.filterForm.value;
    this.currentFilters = {
      dniAsesor: dniAsesor?.trim() || null,
      nombreAsesor: nombreAsesor?.trim() || null,
      numeroMovil: numeroMovil?.trim() || null,
      fecha: fecha ? fecha.toString() : null,
    };
    this.currentPage = 0;

    // Resetear el paginador a la primera página
    if (this.paginator) {
      this.paginator.pageIndex = 0;
    }

    this.store.dispatch(
      fromClienteActions.loadClientesFiltrados({
        dniAsesor: this.currentFilters.dniAsesor,
        nombreAsesor: this.currentFilters.nombreAsesor,
        numeroMovil: this.currentFilters.numeroMovil,
        fecha: this.currentFilters.fecha,
        page: this.currentPage,
        size: this.pageSize,
      })
    );
  }

  limpiarFiltros(): void {
    this.filterForm.reset();
    this.currentFilters = null;
    this.currentPage = 0;

    // Resetear el paginador a la primera página
    if (this.paginator) {
      this.paginator.pageIndex = 0;
    }

    this.loadClientesPage(this.currentPage, this.pageSize);
  }
  /**
   * Verifica si el tema oscuro está activo
   */
  checkDarkTheme(): void {
    // Verificar si el body tiene la clase dark-theme
    const newThemeValue = document.body.classList.contains('dark-theme');

    // Solo actualizar si el valor ha cambiado para evitar ciclos de detección innecesarios
    if (this.isDarkTheme !== newThemeValue) {
      this.isDarkTheme = newThemeValue;
      // Notificar a Angular sobre el cambio fuera del ciclo de detección normal
      this.cdr.detectChanges();
    }
  }

  /**
   * Observa cambios en el tema
   */
  observeThemeChanges(): void {
    // Crear un observer para detectar cambios en el body
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          // Ejecutar fuera del ciclo de detección de cambios de Angular
          setTimeout(() => {
            this.checkDarkTheme();
          }, 0);
        }
      });
    });

    // Iniciar la observación del body
    observer.observe(document.body, { attributes: true });
  }

  // Variable para controlar si ya se ha enviado una petición
  private isRequestInProgress = false;

  openDetails(cliente: ClienteConUsuarioDTO): void {
    // Evitar múltiples peticiones si ya hay una en progreso
    if (this.isRequestInProgress || this.modalVisible) {
      return;
    }

    // Marcar que hay una petición en progreso y mostrar el modal
    this.isRequestInProgress = true;
    this.modalVisible = true;

    // Limpiar cualquier error anterior
    this.store.dispatch(fromClienteActions.clearClienteError());

    this.selectedAdvisorName = cliente.asesor;

    // Formatear la fecha según el tipo de dato
    let fechaCreacion = '';

    // Si es un array, convertir a fecha ISO estándar con todos los componentes
    if (
      Array.isArray(cliente.fechaIngresado) &&
      cliente.fechaIngresado.length >= 3
    ) {
      // Extraer los componentes de fecha y hora del array
      const year = cliente.fechaIngresado[0];
      const month = String(cliente.fechaIngresado[1]).padStart(2, '0');
      const day = String(cliente.fechaIngresado[2]).padStart(2, '0');
      const hours =
        cliente.fechaIngresado.length >= 4
          ? String(cliente.fechaIngresado[3]).padStart(2, '0')
          : '00';
      const minutes =
        cliente.fechaIngresado.length >= 5
          ? String(cliente.fechaIngresado[4]).padStart(2, '0')
          : '00';
      const seconds =
        cliente.fechaIngresado.length >= 6
          ? String(cliente.fechaIngresado[5]).padStart(2, '0')
          : '00';
      // Formatear milisegundos correctamente
      let millis = '000';
      if (cliente.fechaIngresado.length >= 7) {
        // Convertir el valor de nanosegundos a milisegundos (dividir por 1,000,000)
        const nanos = cliente.fechaIngresado[6];
        millis = String(Math.floor(nanos / 1000000)).padStart(3, '0');
      }

      // Crear una fecha ISO completa
      fechaCreacion = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${millis}`;
    }
    // Si es un string, convertir a formato ISO completo
    else if (typeof cliente.fechaIngresado === 'string') {
      try {
        // Intentar convertir a fecha ISO completa
        const date = new Date(cliente.fechaIngresado);
        if (!isNaN(date.getTime())) {
          // Usar directamente toISOString para obtener el formato completo
          fechaCreacion = date.toISOString().replace('Z', '');
        } else {
          fechaCreacion = cliente.getFechaCreacionFormatted();
        }
      } catch (e) {
        fechaCreacion = cliente.getFechaCreacionFormatted();
      }
    }

    // Suscribirse al estado de carga para detectar cuando se completa la petición
    this.loading$.pipe(skip(1), take(1)).subscribe((loading) => {
      // Cuando la carga termina (loading = false), resetear la bandera
      if (!loading) {
        this.isRequestInProgress = false;
      }
    });

    // Verificar que la fecha sea válida antes de enviarla
    if (fechaCreacion) {
      this.store.dispatch(
        fromClienteActions.loadClienteDetalle({
          dni: cliente.dni,
          mobile: cliente.numeroMovil,
          fechaCreacion: fechaCreacion,
        })
      );
    } else {
      // Si la fecha no es válida, enviar una cadena vacía
      this.store.dispatch(
        fromClienteActions.loadClienteDetalle({
          dni: cliente.dni,
          mobile: cliente.numeroMovil,
          fechaCreacion: '',
        })
      );
    }

    // Verificar el tema actual
    const isDark = document.body.classList.contains('dark-theme');
    this.isDarkTheme = isDark;

    // Notificar a Angular sobre el cambio
    this.cdr.detectChanges();

    // Asegurarse de que el modal tenga el tema correcto
    setTimeout(() => {
      const modalElement = document.querySelector('.azulino-modal');
      if (modalElement && isDark) {
        modalElement.classList.add('dark-theme');
      }
    }, 100);
  }

  closeModal(): void {
    this.modalVisible = false;
    // Limpiar el error al cerrar el modal
    this.store.dispatch(fromClienteActions.clearClienteError());
    // Resetear la bandera de petición en progreso
    this.isRequestInProgress = false;

    // Ya no necesitamos reabrir el diálogo porque no se cierra al ver detalles
    console.log('Modal cerrado - El diálogo de leads permanece abierto');
  }

  downloadAllExcel(): void {
    this.exportLoading = true;
    const url = `${environment.url}api/clientes/exportar-excel-masivo`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        // Verificar si el blob es un JSON de error (puede ocurrir con respuestas de error)
        if (blob.type === 'application/json') {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              console.error('Error del servidor:', errorJson);
              Swal.fire(
                'Error',
                errorJson.detail || 'Error al generar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire(
                'Error',
                'No se pudo procesar la respuesta del servidor',
                'error'
              );
            }
            this.exportLoading = false;
          };
          reader.readAsText(blob);
          return;
        }

        this.downloadFile(blob, 'Clientes_Masivo.xlsx');
        this.exportLoading = false;
      },
      error: (error) => {
        console.error('Error al descargar Excel masivo:', error);

        // Intentar leer el cuerpo del error para mostrar un mensaje más específico
        if (error.error instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              Swal.fire(
                'Error',
                errorJson.detail || 'No se pudo descargar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire(
                'Error',
                'No se pudo descargar el Excel masivo',
                'error'
              );
            }
            this.exportLoading = false;
          };
          reader.readAsText(error.error);
        } else {
          Swal.fire('Error', 'No se pudo descargar el Excel masivo', 'error');
          this.exportLoading = false;
        }
      },
    });
  }

  // El método downloadIndividualExcel ha sido reemplazado por downloadModalExcel

  /**
   * Verifica si un valor es un array
   * @param value Valor a verificar
   * @returns true si es un array, false en caso contrario
   */
  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  /**
   * Formatea un array de fecha [año, mes, día, hora, minuto, segundo] a un string dd/MM/yyyy
   * @param dateArray Array de fecha
   * @returns String con formato dd/MM/yyyy
   */
  formatDateArray(dateArray: any[]): string {
    if (!dateArray || dateArray.length < 3) {
      return '-';
    }

    const year = dateArray[0];
    const month = String(dateArray[1]).padStart(2, '0');
    const day = String(dateArray[2]).padStart(2, '0');

    // Formato en texto
    return this.formatDateToText(day, month, year);
  }

  /**
   * Formatea una fecha en formato string a texto
   * @param dateString Fecha en formato string
   * @returns Fecha en formato de texto
   */
  formatStringDateToText(dateString: string): string {
    if (!dateString) {
      return '-';
    }

    try {
      // Intentar convertir a fecha
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString; // Si no es una fecha válida, devolver el string original
      }

      // Extraer día, mes y año
      const day = date.getDate();
      const month = date.getMonth() + 1; // getMonth() devuelve 0-11
      const year = date.getFullYear();

      // Formatear en texto
      return this.formatDateToText(day, month, year);
    } catch (e) {
      console.error('Error al formatear fecha:', e);
      return dateString;
    }
  }

  /**
   * Convierte una fecha a formato de texto en español
   * @param day Día del mes
   * @param month Mes (1-12)
   * @param year Año
   * @returns Fecha en formato de texto (ej: "15 de Enero de 2023")
   */
  formatDateToText(
    day: string | number,
    month: string | number,
    year: string | number
  ): string {
    // Convertir a números para asegurar el formato correcto
    const dayNum = typeof day === 'string' ? parseInt(day, 10) : day;
    const monthNum = typeof month === 'string' ? parseInt(month, 10) : month;
    const yearNum = typeof year === 'string' ? parseInt(year, 10) : year;

    // Nombres de los meses en español
    const monthNames = [
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ];

    // Obtener el nombre del mes (restamos 1 porque los arrays empiezan en 0)
    const monthName = monthNames[monthNum - 1];

    // Formatear la fecha en texto
    return `${dayNum} de ${monthName} de ${yearNum}`;
  }

  /**
   * Formatea un array de fecha [año, mes, día, hora, minuto, segundo] a un string dd/MM/yyyy HH:mm
   * @param dateValue Array de fecha o string
   * @returns String con formato dd/MM/yyyy HH:mm
   */
  formatDateArrayWithTime(dateValue: any): string {
    // Si es un string, intentar usar el pipe date
    if (typeof dateValue === 'string') {
      try {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return `${date.getDate().toString().padStart(2, '0')}/${(
            date.getMonth() + 1
          )
            .toString()
            .padStart(2, '0')}/${date.getFullYear()} ${date
            .getHours()
            .toString()
            .padStart(2, '0')}:${date
            .getMinutes()
            .toString()
            .padStart(2, '0')}`;
        }
      } catch (e) {
        // Si hay error, continuar con el siguiente bloque
      }
    }

    // Si es un string que parece un array (como "2025,3,10,7,33,38")
    if (typeof dateValue === 'string' && dateValue.includes(',')) {
      try {
        const dateArray = dateValue
          .split(',')
          .map((item) => parseInt(item.trim()));
        if (dateArray.length >= 3) {
          const year = dateArray[0];
          const month = String(dateArray[1]).padStart(2, '0');
          const day = String(dateArray[2]).padStart(2, '0');

          // Agregar hora y minutos si están disponibles
          let timeStr = '';
          if (dateArray.length >= 5) {
            const hour = String(dateArray[3]).padStart(2, '0');
            const minute = String(dateArray[4]).padStart(2, '0');
            timeStr = ` ${hour}:${minute}`;
          }

          return `${day}/${month}/${year}${timeStr}`;
        }
      } catch (e) {
        // Si hay error, continuar con el siguiente bloque
      }
    }

    // Si es un array
    if (Array.isArray(dateValue) && dateValue.length >= 3) {
      const year = dateValue[0];
      const month = String(dateValue[1]).padStart(2, '0');
      const day = String(dateValue[2]).padStart(2, '0');

      // Agregar hora y minutos si están disponibles
      let timeStr = '';
      if (dateValue.length >= 5) {
        const hour = String(dateValue[3]).padStart(2, '0');
        const minute = String(dateValue[4]).padStart(2, '0');
        timeStr = ` ${hour}:${minute}`;
      }

      return `${day}/${month}/${year}${timeStr}`;
    }

    // Si no se pudo formatear
    return '-';
  }

  private downloadFile(blob: Blob, fileName: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Función para abrir el diálogo y exportar Excel por fecha integrando el spinner
  downloadExcelByDate(): void {
    this.exportLoading = true;

    const dialogRef = this.dialog.open(ExportByDateDialogComponent, {
      width: '300px',
    });

    dialogRef.afterClosed().subscribe((selectedDate: Date | null) => {
      if (selectedDate) {
        const formattedDate = formatDate(selectedDate, 'yyyy-MM-dd', 'en-US');
        const url = `${
          environment.url
        }api/clientes/exportar-excel-por-fecha?fecha=${encodeURIComponent(
          formattedDate
        )}`;

        this.http.get(url, { responseType: 'blob' }).subscribe({
          next: (blob) => {
            // Verificar si el blob es un JSON de error (puede ocurrir con respuestas de error)
            if (blob.type === 'application/json') {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorJson = JSON.parse(reader.result as string);
                  console.error('Error del servidor:', errorJson);
                  Swal.fire(
                    'Error',
                    errorJson.detail || 'Error al generar el Excel',
                    'error'
                  );
                } catch (e) {
                  Swal.fire(
                    'Error',
                    'No se pudo procesar la respuesta del servidor',
                    'error'
                  );
                }
                this.exportLoading = false;
              };
              reader.readAsText(blob);
              return;
            }

            this.downloadFile(blob, `Clientes_${formattedDate}.xlsx`);
            // Cerramos el spinner solo cuando el Excel se ha generado exitosamente
            this.exportLoading = false;
          },
          error: (error) => {
            console.error('Error al descargar Excel por fecha:', error);

            // Intentar leer el cuerpo del error para mostrar un mensaje más específico
            if (error.error instanceof Blob) {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorJson = JSON.parse(reader.result as string);
                  Swal.fire(
                    'Error',
                    errorJson.detail || 'No se pudo descargar el Excel',
                    'error'
                  );
                } catch (e) {
                  Swal.fire(
                    'Error',
                    'No se pudo descargar el Excel por fecha',
                    'error'
                  );
                }
                this.exportLoading = false;
              };
              reader.readAsText(error.error);
            } else {
              Swal.fire(
                'Error',
                'No se pudo descargar el Excel por fecha',
                'error'
              );
              this.exportLoading = false;
            }
          },
        });
      } else {
        this.exportLoading = false;
      }
    });
  }

  /**
   * Verifica si el coordinador es un objeto o un string
   * @param coordinador El coordinador a verificar
   * @returns true si es un objeto, false si es un string o null
   */
  isCoordinadorObject(coordinador: any): boolean {
    return (
      coordinador &&
      typeof coordinador === 'object' &&
      coordinador.nombre !== undefined
    );
  }

  /**
   * Obtiene el nombre del coordinador
   * @param coordinador El coordinador
   * @returns El nombre del coordinador o una cadena vacía
   */
  getCoordinadorNombre(coordinador: any): string {
    if (this.isCoordinadorObject(coordinador)) {
      return coordinador.nombre || '';
    }
    return '';
  }

  /**
   * Obtiene el apellido del coordinador
   * @param coordinador El coordinador
   * @returns El apellido del coordinador o una cadena vacía
   */
  getCoordinadorApellido(coordinador: any): string {
    if (this.isCoordinadorObject(coordinador)) {
      return coordinador.apellido || '';
    }
    return '';
  }

  /**
   * Obtiene el DNI del coordinador
   * @param coordinador El coordinador
   * @returns El DNI del coordinador o una cadena vacía
   */
  getCoordinadorDni(coordinador: any): string {
    if (this.isCoordinadorObject(coordinador)) {
      return coordinador.dni || '';
    }
    return '';
  }

  /**
   * Verifica si el coordinador tiene fecha de creación
   * @param coordinador El coordinador
   * @returns true si tiene fecha de creación, false en caso contrario
   */
  hasCoordinadorFechaCreacion(coordinador: any): boolean {
    return (
      this.isCoordinadorObject(coordinador) &&
      coordinador.fechaCreacion &&
      Array.isArray(coordinador.fechaCreacion) &&
      coordinador.fechaCreacion.length >= 6
    );
  }

  /**
   * Formatea la fecha de creación del coordinador
   * @param coordinador El coordinador
   * @returns La fecha formateada o una cadena vacía
   */
  formatCoordinadorFechaCreacion(coordinador: any): string {
    if (this.hasCoordinadorFechaCreacion(coordinador)) {
      return this.formatDateArrayWithTime(coordinador.fechaCreacion);
    }
    return '';
  }

  /**
   * Genera un array de números basado en el valor de numeroMoviles
   * @param numeroMoviles El número de móviles como string
   * @returns Un array con índices de 0 a n-1
   */
  getNumberArray(numeroMoviles: string): number[] {
    // Intentar convertir a número
    const num = parseInt(numeroMoviles, 10);

    // Si es un número válido, crear un array con ese número de elementos
    if (!isNaN(num) && num > 0) {
      return Array(num)
        .fill(0)
        .map((_, i) => i);
    }

    // Si no es un número válido, devolver un array vacío
    return [];
  }

  // Activa el modo edición y rellena el formulario con los valores actuales
  enableEdit(): void {
    this.editMode = true;
    this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
      if (cliente) {
        // Asegurarse de que movilesAPortar sea un array
        if (!cliente.movilesAPortar) {
          cliente.movilesAPortar = [];
        }

        this.editForm.patchValue({
          id: cliente.id,
          campania: cliente.campania,
          nombresApellidos: cliente.nombresApellidos,
          nifNie: cliente.nifNie,
          nacionalidad: cliente.nacionalidad,
          fechaNacimiento: cliente.fechaNacimiento
            ? formatDate(cliente.fechaNacimiento, 'yyyy-MM-dd', 'en-US')
            : '',
          genero: cliente.genero,
          correoElectronico: cliente.correoElectronico,
          cuentaBancaria: cliente.cuentaBancaria,
          permanencia: cliente.permanencia,
          direccion: cliente.direccion,
          velocidad: cliente.velocidad,
          movilContacto: cliente.movilContacto,
          fijoCompania: cliente.fijoCompania,
          planActual: cliente.planActual,
          codigoPostal: cliente.codigoPostal,
          provincia: cliente.provincia,
          distrito: cliente.distrito,
          ciudad: cliente.ciudad,
          tipoPlan: cliente.tipoPlan,
          icc: cliente.icc,
          autorizaSeguros: cliente.autorizaSeguros,
          autorizaEnergias: cliente.autorizaEnergias,
          ventaRealizada: cliente.ventaRealizada,
          deseaPromocionesLowi: cliente.deseaPromocionesLowi,
          numeroMoviles: cliente.numeroMoviles,
          tipoFibra: cliente.tipoFibra,
          movilesAPortar: cliente.movilesAPortar,
          observacion: cliente.observacion,
          numeroAgente: cliente.numeroAgente,
          estadoLlamada: cliente.estadoLlamada,
          titularDelServicio: cliente.titularDelServicio,
          futbol: cliente.futbol,
          nombres: cliente.nombres,
          fechaCreacion: cliente.fechaCreacion,
          tipoTecnologia: cliente.tipoTecnologia,
          observacionEstado: cliente.observacionEstado,
        });
      }
    });
  }

  // Cancela la edición y vuelve a modo lectura
  cancelEdit(): void {
    this.editMode = false;
  }

  // Al guardar, despacha la acción para actualizar vía PUT
  // Modify your onUpdateSubmit method to use the loading$ observable
  onUpdateSubmit(): void {
    if (this.editForm.valid) {
      const updatedClient: ClienteResidencial = this.editForm.value;

      // Dispatch the update action
      this.store.dispatch(
        fromClienteActions.updateClient({
          id: updatedClient.id,
          client: updatedClient,
        })
      );

      // Use the loading$ observable to detect when the update completes
      const subscription = this.loading$
        .pipe(
          // Skip the initial loading=true state
          skip(1),
          // Take the next loading state (should be false when update completes)
          take(1)
        )
        .subscribe(() => {
          // Update completed
          this.editMode = false;

          // Refresh the client details
          if (updatedClient.movilContacto) {
            this.store.dispatch(
              fromClienteActions.loadClienteByMobile({
                mobile: updatedClient.movilContacto,
              })
            );
          }

          // Refresh the list
          this.loadClientesPage(this.currentPage, this.pageSize);

          // Clean up
          subscription.unsubscribe();
        });
    }
  }

  // Método para eliminar un móvil de la lista
  removeMovil(movil: string): void {
    const moviles = this.editForm.get('movilesAPortar')?.value || [];
    const index = moviles.indexOf(movil);
    if (index >= 0) {
      moviles.splice(index, 1);
      this.editForm.patchValue({ movilesAPortar: moviles });
    }
  }

  // Método para agregar un móvil a la lista
  addMovil(event: any): void {
    const value = (event.value || '').trim();
    if (value) {
      const moviles = this.editForm.get('movilesAPortar')?.value || [];
      if (!moviles.includes(value)) {
        moviles.push(value);
        this.editForm.patchValue({ movilesAPortar: moviles });
      }
      event.chipInput!.clear();
    }
  }

  isAdmin(): boolean {
    return this.user?.role === 'ADMIN' && !!localStorage.getItem('token');
  }

  isBackOffice(): boolean {
    return this.user?.role === 'BACKOFFICE' && !!localStorage.getItem('token');
  }

  downloadOrPrint(elementId: string): void {
    const element = document.getElementById(elementId);
    if (!element) return;

    // Mostrar indicador de carga
    this.exportLoading = true;

    // Cargar el logo primero
    const logoImg = new Image();
    logoImg.src = 'assets/logovector-MIDAS.svg';

    logoImg.onload = () => {
      // Convertir el SVG a PNG usando canvas
      const canvas = document.createElement('canvas');
      const logoWidth = 300; // Ancho del logo en el canvas (mayor resolución)
      const logoHeight = 100; // Altura del logo en el canvas
      canvas.width = logoWidth;
      canvas.height = logoHeight;

      const ctx = canvas.getContext('2d');
      if (ctx) {
        // Fondo transparente
        ctx.clearRect(0, 0, logoWidth, logoHeight);
        // Dibujar el logo en el canvas
        ctx.drawImage(logoImg, 0, 0, logoWidth, logoHeight);
        // Convertir a formato de imagen
        const logoDataUrl = canvas.toDataURL('image/png');

        // Ahora proceder con la generación del PDF
        this.generatePDFWithLogo(element, logoDataUrl);
      } else {
        // Si no se puede obtener el contexto del canvas, generar PDF sin logo
        this.generatePDFWithoutLogo(element);
      }
    };

    logoImg.onerror = () => {
      console.error('Error al cargar el logo');
      // Continuar sin el logo en caso de error
      this.generatePDFWithoutLogo(element);
    };
  }

  // Método para generar PDF con logo
  private generatePDFWithLogo(element: HTMLElement, logoDataUrl: string): void {
    // Configuración para html2canvas
    const options = {
      scale: 2, // Mejor calidad
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight,
      allowTaint: true,
      backgroundColor: null,
    };

    html2canvas(element, options)
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        // Añadir el logo en la parte superior
        const logoWidth = 50; // Ancho del logo en mm en el PDF
        const logoHeight = 15; // Altura del logo en mm en el PDF
        const logoX = (pdfWidth - logoWidth) / 2; // Centrar horizontalmente
        const logoY = 10; // Margen superior para el logo

        // Añadir el logo al PDF como PNG
        pdf.addImage(logoDataUrl, 'PNG', logoX, logoY, logoWidth, logoHeight);

        // Calcular la altura proporcional manteniendo la relación de aspecto
        const contentHeight = (canvas.height * pdfWidth) / canvas.width;

        // Margen superior después del logo
        const marginTop = logoY + logoHeight + 5; // 5mm de espacio adicional después del logo

        // Si el contenido es más alto que una página, dividirlo en múltiples páginas
        if (contentHeight > pageHeight - marginTop) {
          let remainingHeight = contentHeight;
          let position = 0;

          // Primera página (con logo)
          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
          remainingHeight -= pageHeight - marginTop;
          position += pageHeight - marginTop;

          // Páginas adicionales si es necesario
          while (remainingHeight > 0) {
            pdf.addPage();

            // Añadir el logo en cada página nueva
            pdf.addImage(
              logoDataUrl,
              'PNG',
              logoX,
              logoY,
              logoWidth,
              logoHeight
            );

            pdf.addImage(
              imgData,
              'PNG',
              0,
              -(position - marginTop),
              pdfWidth,
              contentHeight,
              '',
              'FAST'
            );

            remainingHeight -= pageHeight;
            position += pageHeight;
          }
        } else {
          // Si cabe en una sola página
          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
        }

        this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
          pdf.save(`Cliente-${cliente?.movilContacto || 'detalle'}.pdf`);
          // Ocultar indicador de carga
          this.exportLoading = false;
        });
      })
      .catch((error) => {
        console.error('Error al generar PDF:', error);
        Swal.fire(
          'Error',
          'No se pudo generar el PDF. Intente nuevamente.',
          'error'
        );
        this.exportLoading = false;
      });
  }

  // Método auxiliar para generar PDF sin logo en caso de error al cargar el logo
  private generatePDFWithoutLogo(element: HTMLElement): void {
    const options = {
      scale: 2,
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight,
      allowTaint: true,
      backgroundColor: null,
    };

    html2canvas(element, options)
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const contentHeight = (canvas.height * pdfWidth) / canvas.width;
        const pageHeight = pdf.internal.pageSize.getHeight();
        const marginTop = 10; // Margen superior sin logo

        if (contentHeight > pageHeight - marginTop) {
          let remainingHeight = contentHeight;
          let position = 0;

          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
          remainingHeight -= pageHeight - marginTop;
          position += pageHeight - marginTop;

          while (remainingHeight > 0) {
            pdf.addPage();
            pdf.addImage(
              imgData,
              'PNG',
              0,
              -(position - marginTop),
              pdfWidth,
              contentHeight,
              '',
              'FAST'
            );

            remainingHeight -= pageHeight;
            position += pageHeight;
          }
        } else {
          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
        }

        this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
          pdf.save(`Cliente-${cliente?.movilContacto || 'detalle'}.pdf`);
          this.exportLoading = false;
        });
      })
      .catch((error) => {
        console.error('Error al generar PDF:', error);
        Swal.fire(
          'Error',
          'No se pudo generar el PDF. Intente nuevamente.',
          'error'
        );
        this.exportLoading = false;
      });
  }

  downloadModalExcel(clienteParam?: any): void {
    this.exportLoading = true;

    // Determinar el número móvil del cliente
    if (clienteParam) {
      // Si se proporciona un cliente como parámetro
      const numeroMovil =
        clienteParam.numeroMovil || clienteParam.movilContacto;
      if (numeroMovil) {
        this.downloadExcelFromBackend(numeroMovil);
      } else {
        Swal.fire(
          'Error',
          'No se pudo identificar el número móvil del cliente',
          'error'
        );
        this.exportLoading = false;
      }
    } else {
      // Si no se proporciona un cliente, usar el cliente seleccionado
      this.selectedCliente$.pipe(take(1)).subscribe((selectedCliente) => {
        if (selectedCliente && selectedCliente.movilContacto) {
          this.downloadExcelFromBackend(selectedCliente.movilContacto);
        } else {
          Swal.fire(
            'Error',
            'No se pudo identificar el número móvil del cliente',
            'error'
          );
          this.exportLoading = false;
        }
      });
    }
  }

  /**
   * Abre el modal de transcripción para subir un archivo de audio
   * @param cliente Cliente seleccionado
   */
  openAudioUpload(cliente: any): void {
    // Verificar que el cliente tenga un número móvil
    const numeroMovil = cliente.numeroMovil || cliente.movilContacto;
    const fechaIngresado = cliente.fechaIngresado;
    if (!numeroMovil) {
      Swal.fire(
        'Error',
        'El cliente debe tener un número móvil para poder transcribir archivos de audio.',
        'error'
      );
      return;
    }

    // Mostrar loading mientras se verifica si ya existe transcripción
    Swal.fire({
      title: 'Verificando transcripción...',
      text: 'Consultando si ya existe una transcripción para este cliente',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Primero verificar si ya existe una transcripción en la API de detalle
    this.verificarTranscripcionExistente(cliente, numeroMovil,fechaIngresado).then((datosLead) => {
      Swal.close(); // Cerrar el loading

      if (datosLead && datosLead.textoTranscription && datosLead.textoTranscription.trim() !== '') {
        // Ya existe una transcripción, mostrar directamente los resultados
        this.mostrarTranscripcionExistente(datosLead, cliente);
      } else {
        // No hay transcripción, abrir el modal normal de subida
        this.abrirModalTranscripcionNormal(cliente, numeroMovil);
      }
    }).catch((error) => {
      Swal.close(); // Cerrar el loading
      console.error('❌ Error al verificar transcripción existente:', error);

      // En caso de error, continuar con el flujo normal
      this.abrirModalTranscripcionNormal(cliente, numeroMovil);
    });
  }

  /**
   * Verifica si ya existe una transcripción para el cliente en la API de detalle
   */
  private verificarTranscripcionExistente(cliente: any, numeroMovil: string,fechaIngresado: string): Promise<any> {
    return new Promise((resolve, reject) => {
      // Obtener parámetros necesarios para la consulta
      // TODO: Estos valores deberían ser dinámicos según el cliente real
      const dni = '73109458'; // Obtener del cliente real
      const fechaCreacion = '2025-06-02T13:07:56.851'; // Obtener fecha real del cliente

      // Construir la URL de la API de detalle
      const apiUrl = `https://apisozarusac.com/BackendJava/api/cliente-promocion/detalle?dni=${dni}&movil=${numeroMovil}&fechaCreacion=${fechaCreacion}`;

      console.log('=== VERIFICANDO TRANSCRIPCIÓN EXISTENTE ===');
      console.log('URL:', apiUrl);
      console.log('Cliente:', cliente);
      console.log('Número móvil:', numeroMovil);
      console.log('==========================================');

      this.http.get(apiUrl).subscribe({
        next: (response: any) => {
          console.log('✅ Datos del lead obtenidos:', response);
          resolve(response);
        },
        error: (error) => {
          console.error('❌ Error al obtener datos del lead:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * Muestra la transcripción existente encontrada
   */
  private mostrarTranscripcionExistente(datosLead: any, cliente: any): void {
    console.log('✅ Transcripción existente encontrada, mostrando directamente');

    Swal.fire({
      title: '📄 Transcripción Existente Encontrada',
      html: `
        <div style="text-align: left; margin-bottom: 20px;">
          <p><strong>Cliente:</strong> ${cliente.nombres || cliente.nombresApellidos || 'Cliente'}</p>
          <p><strong>Móvil:</strong> ${cliente.numeroMovil || cliente.movilContacto}</p>
          <p><strong>Fecha:</strong> ${datosLead.fechaCreacion || 'No disponible'}</p>
        </div>
        <div style="text-align: left; max-height: 300px; overflow-y: auto; padding: 15px; background: #f8f9fa; border-radius: 8px; font-family: monospace; white-space: pre-wrap; border: 1px solid #dee2e6;">
          ${datosLead.textoTranscription}
        </div>
      `,
      width: '80%',
      showCancelButton: true,
      confirmButtonText: '🔍 Comparar con IA',
      cancelButtonText: '📋 Copiar Texto',
      showDenyButton: true,
      denyButtonText: '❌ Cerrar',
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-secondary',
        denyButton: 'btn btn-outline-secondary'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // Usuario quiere comparar con IA
        this.compararTranscripcionConIA(datosLead);
      } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
        // Usuario quiere copiar el texto
        navigator.clipboard.writeText(datosLead.textoTranscription).then(() => {
          Swal.fire('¡Copiado!', 'El texto ha sido copiado al portapapeles.', 'success');
        });
      }
    });
  }

  /**
   * Abre el modal normal de transcripción para subir archivos
   */
  private abrirModalTranscripcionNormal(cliente: any, numeroMovil: string): void {
    // Configurar los datos para el modal de transcripción
    const dialogData: TranscriptionDialogData = {
      allowFileUpload: true, // Habilitar subida de archivos
      cliente: {
        nombres: cliente.nombres || cliente.nombresApellidos || 'Cliente',
        apellidos: cliente.apellidos || ''
      },
      fechaIngresado: cliente.fechaIngresado,
      numeroMovil: numeroMovil
    };

    // Abrir el modal de transcripción directamente
    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '95vw',
      maxWidth: '1000px',
      height: '95vh',
      maxHeight: '900px',
      disableClose: false,
      data: dialogData,
      panelClass: 'transcription-modal'
    });

    // Manejar el resultado cuando se cierre el modal
    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('✅ Transcripción completada para cliente:', cliente.nombres || cliente.nombresApellidos);
        console.log('📄 Resultado:', result);

        Swal.fire({
          title: '¡Transcripción Completada!',
          text: 'El archivo de audio ha sido transcrito exitosamente.',
          icon: 'success',
          confirmButtonText: 'Ver Resultado',
          showCancelButton: true,
          cancelButtonText: 'Cerrar'
        }).then((swalResult) => {
          if (swalResult.isConfirmed && result.transcription) {
            // Mostrar el texto transcrito en un modal
            Swal.fire({
              title: 'Texto Transcrito',
              html: `<div style="text-align: left; max-height: 400px; overflow-y: auto; padding: 10px; background: #f8f9fa; border-radius: 5px; font-family: monospace; white-space: pre-wrap;">${result.transcription}</div>`,
              width: '80%',
              confirmButtonText: 'Copiar al Portapapeles',
              showCancelButton: true,
              cancelButtonText: 'Cerrar'
            }).then((copyResult) => {
              if (copyResult.isConfirmed) {
                // Copiar al portapapeles
                navigator.clipboard.writeText(result.transcription).then(() => {
                  Swal.fire('¡Copiado!', 'El texto ha sido copiado al portapapeles.', 'success');
                });
              }
            });
          }
        });
      } else if (result === false) {
        console.log('❌ Usuario canceló la transcripción');
      }
    });
  }

  /**
   * Compara la transcripción existente con los datos del lead usando IA
   */
  private compararTranscripcionConIA(datosLead: any): void {
    // Mostrar loading mientras se procesa la comparación
    Swal.fire({
      title: '🤖 Comparando con IA...',
      text: 'Analizando la transcripción con los datos del lead',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Preparar los datos para enviar a la API de comparación
    const requestData = {
      texto_audio: datosLead.textoTranscription,
      datos_lead: datosLead // Usar todos los datos completos del lead
    };

    console.log('=== ENVIANDO DATOS A API DE COMPARACIÓN ===');
    console.log('URL:', 'http://127.0.0.1:8000/api/comparar/');
    console.log('Datos enviados:', requestData);
    console.log('==========================================');

    // Enviar datos a la API de comparación
    this.http.post('http://127.0.0.1:8000/api/comparar/', requestData).subscribe({
      next: (response: any) => {
        console.log('✅ Respuesta exitosa de la API de comparación:', response);
        Swal.close(); // Cerrar el loading

        // Extraer el porcentaje de coincidencia de la respuesta
        const porcentajeCoincidencia = response.porcentaje_coincidencia || 0;

        // Determinar el color y mensaje según el porcentaje
        let colorPorcentaje = '#059669'; // Verde por defecto
        let mensajeCalidad = 'Excelente coincidencia';
        let iconoCalidad = '🎯';

        if (porcentajeCoincidencia >= 80) {
          colorPorcentaje = '#059669'; // Verde
          mensajeCalidad = 'Excelente coincidencia';
          iconoCalidad = '🎯';
        } else if (porcentajeCoincidencia >= 60) {
          colorPorcentaje = '#0891b2'; // Azul
          mensajeCalidad = 'Buena coincidencia';
          iconoCalidad = '✅';
        } else if (porcentajeCoincidencia >= 40) {
          colorPorcentaje = '#ea580c'; // Naranja
          mensajeCalidad = 'Coincidencia moderada';
          iconoCalidad = '⚠️';
        } else {
          colorPorcentaje = '#dc2626'; // Rojo
          mensajeCalidad = 'Baja coincidencia';
          iconoCalidad = '❌';
        }

        // Mostrar resultado de la comparación
        Swal.fire({
          title: '✅ Comparación Completada',
          html: `
            <div style="text-align: center; padding: 30px;">
              <div style="margin-bottom: 30px;">
                <div style="font-size: 72px; margin-bottom: 15px;">${iconoCalidad}</div>
                <h2 style="color: ${colorPorcentaje}; margin: 0 0 10px 0; font-size: 48px; font-weight: bold;">
                  ${porcentajeCoincidencia}%
                </h2>
                <h3 style="color: #374151; margin: 0 0 20px 0; font-size: 24px; font-weight: 500;">
                  ${mensajeCalidad}
                </h3>
              </div>

              <div style="background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); border: 2px solid ${colorPorcentaje}; border-radius: 16px; padding: 25px; margin: 20px 0;">
                <h4 style="color: ${colorPorcentaje}; margin: 0 0 15px 0; font-size: 18px; font-weight: 600;">
                  📊 Porcentaje de Coincidencia
                </h4>
                <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                  <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                    <div style="width: 200px; height: 20px; background: #e5e7eb; border-radius: 10px; overflow: hidden; position: relative;">
                      <div style="width: ${porcentajeCoincidencia}%; height: 100%; background: linear-gradient(90deg, ${colorPorcentaje} 0%, ${colorPorcentaje}dd 100%); border-radius: 10px; transition: width 0.3s ease;"></div>
                    </div>
                    <span style="font-size: 20px; font-weight: bold; color: ${colorPorcentaje};">${porcentajeCoincidencia}%</span>
                  </div>
                </div>
              </div>

              <p style="color: #6b7280; font-size: 16px; margin: 20px 0 0 0; line-height: 1.5;">
                La transcripción del audio ha sido comparada exitosamente con los datos del lead.
              </p>
            </div>
          `,
          icon: 'success',
          iconColor: colorPorcentaje,
          confirmButtonText: 'Entendido',
          confirmButtonColor: colorPorcentaje,
          width: '500px'
        });
      },
      error: (error) => {
        console.error('❌ Error en la API de comparación:', error);
        Swal.close(); // Cerrar el loading

        // Determinar el mensaje de error
        let errorMessage = 'Error desconocido al comparar con la IA';
        if (error.error && error.error.detail) {
          errorMessage = error.error.detail;
        } else if (error.message) {
          errorMessage = error.message;
        }

        Swal.fire({
          title: '❌ Error en la Comparación',
          html: `
            <div style="text-align: center; padding: 20px;">
              <div style="font-size: 64px; margin-bottom: 20px;">😞</div>
              <p style="color: #dc2626; font-size: 18px; margin-bottom: 15px;">
                No se pudo completar la comparación con IA
              </p>
              <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 15px; margin: 15px 0;">
                <p style="color: #991b1b; font-size: 14px; margin: 0;">
                  <strong>Error:</strong> ${errorMessage}
                </p>
              </div>
              <p style="color: #6b7280; font-size: 14px; margin: 0;">
                Por favor, inténtalo de nuevo más tarde o contacta al administrador.
              </p>
            </div>
          `,
          icon: 'error',
          confirmButtonText: 'Cerrar',
          confirmButtonColor: '#dc2626',
          width: '500px'
        });
      }
    });
  }

  // Función para abrir el diálogo y exportar Excel por rango de fechas
  downloadExcelByDateRange(): void {
    this.exportLoading = true;

    const dialogRef = this.dialog.open(ExportByDateRangeDialogComponent, {
      width: '400px',
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const { fechaInicio, fechaFin } = result;
        const formattedFechaInicio = formatDate(
          fechaInicio,
          'yyyy-MM-dd',
          'en-US'
        );
        const formattedFechaFin = formatDate(fechaFin, 'yyyy-MM-dd', 'en-US');

        const url = `${
          environment.url
        }api/clientes/exportar-excel-por-rango-fechas?fechaInicio=${encodeURIComponent(
          formattedFechaInicio
        )}&fechaFin=${encodeURIComponent(formattedFechaFin)}`;

        this.http.get(url, { responseType: 'blob' }).subscribe({
          next: (blob) => {
            // Verificar si el blob es un JSON de error (puede ocurrir con respuestas de error)
            if (blob.type === 'application/json') {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorJson = JSON.parse(reader.result as string);
                  console.error('Error del servidor:', errorJson);
                  Swal.fire(
                    'Error',
                    errorJson.detail || 'Error al generar el Excel',
                    'error'
                  );
                } catch (e) {
                  Swal.fire(
                    'Error',
                    'No se pudo procesar la respuesta del servidor',
                    'error'
                  );
                }
                this.exportLoading = false;
              };
              reader.readAsText(blob);
              return;
            }

            this.downloadFile(
              blob,
              `Clientes_${formattedFechaInicio}_a_${formattedFechaFin}.xlsx`
            );
            this.exportLoading = false;
          },
          error: (error) => {
            console.error(
              'Error al descargar Excel por rango de fechas:',
              error
            );

            // Intentar leer el cuerpo del error para mostrar un mensaje más específico
            if (error.error instanceof Blob) {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorJson = JSON.parse(reader.result as string);
                  Swal.fire(
                    'Error',
                    errorJson.detail || 'No se pudo descargar el Excel',
                    'error'
                  );
                } catch (e) {
                  Swal.fire(
                    'Error',
                    'No se pudo descargar el Excel por rango de fechas',
                    'error'
                  );
                }
                this.exportLoading = false;
              };
              reader.readAsText(error.error);
            } else {
              Swal.fire(
                'Error',
                'No se pudo descargar el Excel por rango de fechas',
                'error'
              );
              this.exportLoading = false;
            }
          },
        });
      } else {
        this.exportLoading = false;
      }
    });
  }

  private downloadExcelFromBackend(numeroMovil: string): void {
    const url = `${environment.url}api/clientes/exportar-excel-individual/${numeroMovil}`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        // Verificar si el blob es un JSON de error (puede ocurrir con respuestas de error)
        if (blob.type === 'application/json') {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              console.error('Error del servidor:', errorJson);
              Swal.fire(
                'Error',
                errorJson.detail || 'Error al generar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire(
                'Error',
                'No se pudo procesar la respuesta del servidor',
                'error'
              );
            }
            this.exportLoading = false;
          };
          reader.readAsText(blob);
          return;
        }

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Cliente-${numeroMovil}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
        this.exportLoading = false;
      },
      error: (error) => {
        console.error('Error al descargar Excel:', error);

        // Intentar leer el cuerpo del error para mostrar un mensaje más específico
        if (error.error instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              Swal.fire(
                'Error',
                errorJson.detail || 'No se pudo descargar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
            }
            this.exportLoading = false;
          };
          reader.readAsText(error.error);
        } else {
          Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
          this.exportLoading = false;
        }
      },
    });
  }

  // Método para navegar a la página de estadísticas
  verMetasYEstadisticas(): void {
    this.router.navigate(['/clienteresidencial/estadisticas']);
  }
}
