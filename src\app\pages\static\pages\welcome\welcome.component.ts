import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  ViewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Store, select } from '@ngrx/store';
import { Router } from '@angular/router';
import * as fromRoot from '../../../../store';
import * as fromUser from '../../../../store/user';
import { User } from '@app/models/backend/user/index';
import { Observable } from 'rxjs';
import { NgForm, FormControl } from '@angular/forms';
import { CatastroService } from '@app/services/catastro.service';
import { environment } from '@src/environments/environment';
import Swal from 'sweetalert2';
import { DateAdapter } from '@angular/material/core';
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
  filter,
} from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { MapDialogComponent } from '@app/pages/mapas/map-dialog/map-dialog.component';

@Component({
  selector: 'app-welcome',
  templateUrl: './welcome.component.html',
  styleUrls: ['./welcome.component.scss'],
})
export class WelcomeComponent implements OnInit, OnDestroy {
  @ViewChild('clienteForm') clienteForm!: NgForm;

  // Estado para mostrar el panel hover del mapa
  showMapHoverPanel: boolean = false;

  // Último tipo de mapa utilizado
  lastMapType: 'leaflet' | 'mapbox' = 'mapbox';

  // Temporizador para el panel hover
  mapHoverTimer: any = null;
  mapHoverTimeRemaining: number = 5; // Tiempo en segundos

  /* days: number[] = Array.from({ length: 31 }, (_, i) => i + 1);
  months = [
    { id: 1, name: 'Enero' },
    { id: 2, name: 'Febrero' },
    { id: 3, name: 'Marzo' },
    { id: 4, name: 'Abril' },
    { id: 5, name: 'Mayo' },
    { id: 6, name: 'Junio' },
    { id: 7, name: 'Julio' },
    { id: 8, name: 'Agosto' },
    { id: 9, name: 'Septiembre' },
    { id: 10, name: 'Octubre' },
    { id: 11, name: 'Noviembre' },
    { id: 12, name: 'Diciembre' }
  ];
  years: number[] = Array.from({ length: 100 }, (_, i) => new Date().getFullYear() - i);
 */
  selectedDay: number | null = null;
  selectedMonth: number | null = null;
  selectedYear: number | null = null;
  isSubmitting: boolean = false;

  isAuthorized: boolean = false;

  // Variables para permanencia
  selectedPermanencia: string = '';
  showDatePicker = false;

  // Listados fijos
  tiposFibra: string[] = ['No tiene', '300 Mbps', '600 Mbps', '1 Gbps'];
  operadores: string[] = [
    'Adamo',
    'Alemobil',
    'Avatel',
    'Euskaltel',
    'Finetwork',
    'Gelpiu',
    'Guuk',
    'Hits Mobile',
    'Jazztel',
    'Lowi',
    'Másmóvil',
    'Movistar',
    'O2',
    'Orange',
    'Pepephone',
    'R Cable',
    'Silbö',
    'Simyo',
    'Vodafone',
    'Yoigo',
    // Nuevos elementos que no estaban en la lista original:
    'DIGI SPAIN TELECOM',
    'LEMONVIL',
    'R CABLE Y TELECOMUNICACIONES',
    'AIRENETWORKS',
    'PTV TELECOM 5G',
    'PEPEPHONE 3.0',
    'TELECABLE',
    'LLAMAYA_GMM',
    'LAYCA',
    'VIRGIN',
    '7PLAY',
    'SEVEN',
    'DIGI',
    'LEBARA',
    'AMENA',
    'PARLEM',
    'HAPPYMOVIL',
  ];

  // Variables para el select de operadores con búsqueda
  operadoresFiltrados: string[] = [];
  operadorFilterControl = new FormControl('');
  mostrarOperadores = false;
  cargandoOperadores = false;
  operadorValido = true; // Para mostrar indicador visual de validación

  // Localización
  ccaaList: any[] = [];
  provinciaList: any[] = [];
  municipioList: any[] = [];
  selectedCcaa: number | null = null;
  selectedProvincia: number | string | null = null;
  selectedMunicipio: string = '';

  // Catastro
  provinciasCatastro: any[] = [];
  municipiosCatastro: any[] = [];
  viasCatastro: any[] = [];
  provinciasCatastroFiltradas: any[] = [];
  municipiosCatastroFiltrados: any[] = [];
  viasCatastroFiltradas: any[] = [];
  provinciaFilterControl = new FormControl('');
  municipioFilterControl = new FormControl('');
  viaFilterControl = new FormControl('');
  selectedProvinciaCatastro: any = null;
  selectedMunicipioCatastro: any = null;
  selectedViaCatastro: any = null;
  cargandoProvincias = false;
  cargandoMunicipios = false;
  cargandoVias = false;
  mostrarProvincias = false;
  mostrarMunicipios = false;
  mostrarVias = false;

  // Usuario
  user$!: Observable<fromUser.UserResponse>;
  userData!: User | null;

  // Datos del formulario
  formData: {
    clienteResidencial: {
      movilContacto: string;
      campania: string;
      nombresApellidos: string;
      nifNie: string;
      tipoFibra: string;
      permanencia: string;
      promocion: string;
      fechaNacimiento: string;
      genero: string;
      numeroMoviles: string;
      planActual: string;
      codigoPostal: string;
      provincia: string;
      distrito: string;
      ciudad: string;
      direccion: string;
      // Nuevos campos para dirección interna
      numero: string;
      bloque: string;
      escalera: string;
      planta: string;
      puerta: string;
      fijoCompania: string;
      movilesAPortar: string[];
      tipoPlan: string;
      icc: string;
      usuarioId?: number;
      nacionalidad: string;
      correoElectronico: string;
      cuentaBancaria: string;
      autorizaSeguros: boolean;
      autorizaEnergias: boolean;
      ventaRealizada: boolean;
      deseaPromocionesLowi: boolean; // Nuevo campo para gestionar si el cliente desea promociones LOWI o costos más bajos
      observacion: string;
      numeroAgente: string;
      estadoLlamada: string;
      titularDelServicio: string;
      futbol: string;
      tipoTecnologia: string;
      velocidad: string;
    };
  } = {
    clienteResidencial: {
      movilContacto: '',
      campania: '',
      nombresApellidos: '',
      nifNie: '',
      tipoFibra: '',
      permanencia: '',
      promocion: '',
      fechaNacimiento: '',
      genero: '',
      numeroMoviles: '',
      planActual: '',
      codigoPostal: '',
      provincia: '',
      distrito: '',
      ciudad: '',
      direccion: '',
      // Nuevos campos para dirección interna
      numero: '',
      bloque: '',
      escalera: '',
      planta: '',
      puerta: '',
      fijoCompania: '',
      movilesAPortar: [],
      tipoPlan: '',
      icc: '',
      usuarioId: undefined,
      nacionalidad: '',
      correoElectronico: '',
      cuentaBancaria: '',
      autorizaSeguros: false,
      autorizaEnergias: false,
      ventaRealizada: false,
      deseaPromocionesLowi: false,
      observacion: '',
      numeroAgente: '',
      estadoLlamada: '',
      titularDelServicio: '',
      futbol: '',
      tipoTecnologia: '',
      velocidad: '',
    },
  };

  // Añadir una propiedad para mantener el número de agente
  private _numeroAgente: string = '';

  get numeroAgente(): string {
    return this._numeroAgente;
  }

  set numeroAgente(value: string) {
    this._numeroAgente = value;
    if (this.formData.clienteResidencial) {
      this.formData.clienteResidencial.numeroAgente = value;
    }
  }

  constructor(
    private http: HttpClient,
    private store: Store<fromRoot.State>,
    private router: Router,
    private catastroService: CatastroService,
    private dateAdapter: DateAdapter<any>,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef
  ) {
    this.dateAdapter.setLocale('es');

    // Agregar evento para cerrar los dropdowns cuando se hace clic fuera de ellos
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;

      // Si el clic no fue en un elemento relacionado con los dropdowns, cerrarlos
      if (
        !target.closest('.provincia-dropdown') &&
        !target.closest('.municipio-dropdown') &&
        !target.closest('.via-dropdown') &&
        !target.closest('.operador-dropdown')
      ) {
        // Usar setTimeout para asegurar que se ejecute después de los eventos de selección
        setTimeout(() => {
          this.mostrarProvincias = false;
          this.mostrarMunicipios = false;
          this.mostrarVias = false;
          this.mostrarOperadores = false;
        }, 0);
      }
    });
  }

  ngOnInit(): void {
    // Suscribirse a los datos del usuario
    this.user$ = this.store.pipe(
      select(fromUser.getUser)
    ) as Observable<fromUser.UserResponse>;
    this.user$.subscribe((user) => {
      if (user) {
        this.userData = user;
        this.formData.clienteResidencial.usuarioId = user.id;
      }
    });

    // Suscribirse a la autorización
    this.store.pipe(select(fromUser.getIsAuthorized)).subscribe((auth) => {
      this.isAuthorized = auth;
    });

    // Restaurar datos pendientes del sessionStorage (si existen)
    const pendingData = sessionStorage.getItem('pendingClienteData');
    if (pendingData) {
      try {
        this.formData = JSON.parse(pendingData);
        //console.log('Se han restaurado los datos pendientes desde sessionStorage.');
        // Opcional: borrar los datos pendientes después de restaurarlos
        sessionStorage.removeItem('pendingClienteData');
      } catch (error) {
        //console.error('Error al restaurar los datos pendientes:', error);
      }
    }

    // Inicializar lista de operadores filtrados
    this.operadoresFiltrados = [...this.operadores];

    // Configurar filtro de operadores
    this.operadorFilterControl.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe((filtro) => {
        this.filtrarOperadores(filtro || '');
      });

    // Cargar provincias automáticamente al iniciar como en ventas
    this.cargarProvincias();

    // Configurar filtro de provincias - filtrar cuando el usuario escribe
    this.provinciaFilterControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((filtro) => {
          this.cargandoProvincias = true;
          this.mostrarProvincias = true; // Mostrar dropdown al escribir
          return this.catastroService.getProvincias(filtro || '');
        })
      )
      .subscribe({
        next: (response) => {
          if (response && response.d) {
            this.provinciasCatastro = response.d;
            this.provinciasCatastroFiltradas = [...this.provinciasCatastro]; // Actualizar lista filtrada
          }
          this.cargandoProvincias = false;
        },
        error: (error) => {
          //console.error('Error al filtrar provincias:', error);
          this.cargandoProvincias = false;
        },
      });

    // Configurar filtro de municipios - filtrar cuando el usuario escribe
    this.municipioFilterControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter(() => this.selectedProvinciaCatastro), // Solo procesar si hay provincia seleccionada
        switchMap((filtro) => {
          this.cargandoMunicipios = true;
          this.mostrarMunicipios = true; // Mostrar dropdown al escribir
          return this.catastroService.getMunicipios(
            this.selectedProvinciaCatastro.Codigo,
            filtro || ''
          );
        })
      )
      .subscribe({
        next: (response) => {
          if (response && response.d) {
            this.municipiosCatastro = response.d;
            this.municipiosCatastroFiltrados = [...this.municipiosCatastro]; // Actualizar lista filtrada
          }
          this.cargandoMunicipios = false;
        },
        error: (error) => {
          //console.error('Error al filtrar municipios:', error);
          this.cargandoMunicipios = false;
        },
      });

    // Configurar filtro de vías - filtrar cuando el usuario escribe
    this.viaFilterControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter(
          () => this.selectedProvinciaCatastro && this.selectedMunicipioCatastro
        ), // Solo procesar si hay provincia y municipio seleccionados
        switchMap((filtro) => {
          this.cargandoVias = true;
          this.mostrarVias = true; // Mostrar dropdown al escribir
          return this.catastroService.getVias(
            this.selectedProvinciaCatastro.Codigo,
            this.selectedMunicipioCatastro.Codigo,
            filtro || ''
          );
        })
      )
      .subscribe({
        next: (response) => {
          if (response && response.d) {
            this.viasCatastro = response.d;
            this.viasCatastroFiltradas = [...this.viasCatastro]; // Actualizar lista filtrada
          }
          this.cargandoVias = false;
        },
        error: (error) => {
          //console.error('Error al filtrar vías:', error);
          this.cargandoVias = false;
        },
      });
  }

  updateDate() {
    if (this.selectedDay && this.selectedMonth && this.selectedYear) {
      const day =
        this.selectedDay < 10 ? `0${this.selectedDay}` : this.selectedDay;
      const month =
        this.selectedMonth < 10 ? `0${this.selectedMonth}` : this.selectedMonth;
      this.formData.clienteResidencial.fechaNacimiento = `${this.selectedYear}-${month}-${day}`;
    }
  }

  onMunicipioChange(event: any) {
    const value = event.value;
    if (value === 'otro') {
      this.selectedMunicipio = 'otro';
      this.formData.clienteResidencial.ciudad = '';
    } else {
      this.selectedMunicipio = value;
      this.formData.clienteResidencial.ciudad = value;
    }
  }

  // Métodos para el Catastro

  // Cargar provincias
  cargarProvincias(): void {
    console.log('Cargando provincias...');
    this.cargandoProvincias = true;
    this.catastroService.getProvincias('').subscribe({
      next: (response) => {
        if (response && response.d) {
          this.provinciasCatastro = response.d;
        } else {
          this.provinciasCatastro = response || [];
        }
        this.provinciasCatastroFiltradas = [...this.provinciasCatastro]; // Inicializar lista filtrada
        this.cargandoProvincias = false;

        // Mostrar el dropdown de provincias
        this.mostrarProvincias = true;
      },
      error: (error) => {
        console.error('Error al cargar provincias:', error);
        this.cargandoProvincias = false;
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudieron cargar las provincias',
          confirmButtonColor: '#d33',
        });
      },
    });
  }

  // Filtrar provincias según lo que escribe el usuario
  filtrarProvincias(event: Event): void {
    const input = event.target as HTMLInputElement;
    const filtro = input.value.toLowerCase();

    console.log('Filtrando provincias con:', filtro);

    // Mostrar el dropdown al escribir
    this.mostrarProvincias = true;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    // Si no hay provincias cargadas, cargarlas
    if (this.provinciasCatastro.length === 0) {
      console.log('No hay provincias cargadas, cargando...');
      this.cargarProvincias();
      return;
    }

    // Filtrar provincias
    if (filtro) {
      console.log('Filtrando de', this.provinciasCatastro.length, 'provincias');
      this.provinciasCatastroFiltradas = this.provinciasCatastro.filter(
        (provincia) => provincia.Denominacion.toLowerCase().includes(filtro)
      );

      console.log('Provincias filtradas:', this.provinciasCatastroFiltradas);

      // Si hay una coincidencia exacta, seleccionarla automáticamente
      const coincidenciaExacta = this.provinciasCatastro.find(
        (provincia) => provincia.Denominacion.toLowerCase() === filtro
      );

      if (coincidenciaExacta) {
        console.log('Coincidencia exacta encontrada:', coincidenciaExacta);
        this.seleccionarProvinciaCatastro(coincidenciaExacta);
      }
    } else {
      this.provinciasCatastroFiltradas = [...this.provinciasCatastro];
    }

    console.log(
      'Filtrando provincias:',
      filtro,
      'Resultados:',
      this.provinciasCatastroFiltradas.length
    );
  }

  cargarMunicipiosCatastro(codigoProvincia: string) {
    this.cargandoMunicipios = true;

    // Usar el método getMunicipios en lugar de getMunicipiosByProvincia
    this.catastroService.getMunicipios(codigoProvincia).subscribe({
      next: (response) => {
        console.log('Municipios cargados:', response);
        if (response && response.d) {
          this.municipiosCatastro = response.d;
        } else {
          this.municipiosCatastro = response || [];
        }
        this.municipiosCatastroFiltrados = [...this.municipiosCatastro]; // Inicializar lista filtrada
        this.cargandoMunicipios = false;

        // Asegurar que el dropdown de provincias esté cerrado
        this.mostrarProvincias = false;

        // Mostrar el dropdown de municipios
        this.mostrarMunicipios = true;

        // Forzar detección de cambios
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error al cargar municipios:', error);
        this.cargandoMunicipios = false;

        // Asegurar que el dropdown de provincias esté cerrado incluso en error
        this.mostrarProvincias = false;
        this.cdr.detectChanges();

        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudieron cargar los municipios',
          confirmButtonColor: '#d33',
        });
      },
    });
  }

  // Filtrar municipios según lo que escribe el usuario
  filtrarMunicipios(event: Event): void {
    const input = event.target as HTMLInputElement;
    const filtro = input.value.toLowerCase();

    console.log('Filtrando municipios con:', filtro);

    // Si no hay provincia seleccionada, no hacer nada
    if (!this.selectedProvinciaCatastro) {
      console.log('No hay provincia seleccionada');
      return;
    }

    // Mostrar el dropdown al escribir
    this.mostrarMunicipios = true;
    this.mostrarProvincias = false;
    this.mostrarVias = false;

    // Si no hay municipios cargados, cargarlos
    if (this.municipiosCatastro.length === 0) {
      console.log('No hay municipios cargados, cargando...');
      this.cargarMunicipiosCatastro(this.selectedProvinciaCatastro.Codigo);
      return;
    }

    // Filtrar municipios
    if (filtro) {
      console.log('Filtrando de', this.municipiosCatastro.length, 'municipios');
      this.municipiosCatastroFiltrados = this.municipiosCatastro.filter(
        (municipio) => municipio.Denominacion.toLowerCase().includes(filtro)
      );

      console.log('Municipios filtrados:', this.municipiosCatastroFiltrados);

      // Si hay una coincidencia exacta, seleccionarla automáticamente
      const coincidenciaExacta = this.municipiosCatastro.find(
        (municipio) => municipio.Denominacion.toLowerCase() === filtro
      );

      if (coincidenciaExacta) {
        console.log('Coincidencia exacta encontrada:', coincidenciaExacta);
        this.seleccionarMunicipioCatastro(coincidenciaExacta);
      }
    } else {
      this.municipiosCatastroFiltrados = [...this.municipiosCatastro];
    }

    console.log(
      'Filtrando municipios:',
      filtro,
      'Resultados:',
      this.municipiosCatastroFiltrados.length
    );
  }

  // Método específico para cerrar dropdowns
  cerrarTodosLosDropdowns(): void {
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;
    // Forzar detección de cambios
    this.cdr.detectChanges();
  }

  // Seleccionar provincia
  seleccionarProvinciaCatastro(provincia: any, index: number = 0): void {
    console.log('Seleccionando provincia:', provincia, 'para índice:', index);

    // FORZAR CIERRE INMEDIATO Y DEFINITIVO
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    this.selectedProvinciaCatastro = provincia;
    this.provinciaFilterControl.setValue(provincia.Denominacion);
    this.formData.clienteResidencial.provincia = provincia.Denominacion;

    // Resetear municipio y vía
    this.selectedMunicipioCatastro = null;
    this.municipioFilterControl.setValue('');
    this.viaFilterControl.setValue('');
    this.municipiosCatastro = [];
    this.viasCatastro = [];
    this.municipiosCatastroFiltrados = [];
    this.viasCatastroFiltradas = [];

    // Actualizar dirección completa
    this.actualizarDireccionCompleta(index);

    // Cargar municipios de la provincia seleccionada DESPUÉS de un delay
    setTimeout(() => {
      // Asegurar que provincias sigue cerrado antes de cargar municipios
      this.mostrarProvincias = false;
      this.cargarMunicipiosCatastro(provincia.Codigo);
    }, 100);

    // Forzar cierre múltiples veces con diferentes delays
    for (let i = 0; i < 10; i++) {
      setTimeout(() => {
        this.mostrarProvincias = false;
        this.cdr.detectChanges();
      }, i * 50);
    }
  }

  seleccionarMunicipioCatastro(municipio: any) {
    // Guardar el objeto municipio completo para tener acceso a su código
    this.selectedMunicipioCatastro = municipio;

    // Actualizar el distrito (que es el campo que se usa para el municipio)
    this.formData.clienteResidencial.distrito = municipio.Denominacion;

    // Actualizar el valor del campo de texto con el nombre del municipio seleccionado
    this.municipioFilterControl.setValue(municipio.Denominacion, {
      emitEvent: false,
    });

    // Cerrar dropdown inmediatamente
    this.mostrarMunicipios = false;

    // Limpiar el campo de vía y las vías cargadas
    this.viaFilterControl.setValue('', { emitEvent: false });
    this.viasCatastro = [];
    this.viasCatastroFiltradas = [];
    this.selectedViaCatastro = null;

    // Ocultar también el dropdown de vías
    this.mostrarVias = false;

    // Actualizar la dirección con la provincia y municipio seleccionados
    this.actualizarDireccionCompleta();

    //console.log('Municipio seleccionado:', municipio.Denominacion, 'Código:', municipio.Codigo);

    // Cargar vías del municipio seleccionado
    this.cargarViasCatastro(
      this.selectedProvinciaCatastro.Codigo,
      municipio.Codigo
    );

    // Asegurar que el dropdown se mantenga cerrado
    setTimeout(() => {
      this.mostrarMunicipios = false;
    }, 50);
  }

  // Filtrar vías según lo que escribe el usuario
  filtrarVias(event: Event): void {
    const input = event.target as HTMLInputElement;
    const filtro = input.value.toLowerCase();

    console.log('Filtrando vías con:', filtro);

    // Si no hay municipio seleccionado, no hacer nada
    if (!this.selectedMunicipioCatastro) {
      console.log('No hay municipio seleccionado');
      return;
    }

    // Mostrar el dropdown al escribir
    this.mostrarVias = true;
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;

    // Si no hay vías cargadas, cargarlas
    if (this.viasCatastro.length === 0) {
      console.log('No hay vías cargadas, cargando...');
      this.cargarViasCatastro(
        this.selectedProvinciaCatastro.Codigo,
        this.selectedMunicipioCatastro.Codigo
      );
      return;
    }

    // Filtrar vías
    if (filtro) {
      console.log('Filtrando de', this.viasCatastro.length, 'vías');
      this.viasCatastroFiltradas = this.viasCatastro.filter((via) =>
        via.DenominacionCompleta.toLowerCase().includes(filtro)
      );

      console.log('Vías filtradas:', this.viasCatastroFiltradas);

      // Si hay una coincidencia exacta, seleccionarla automáticamente
      const coincidenciaExacta = this.viasCatastro.find(
        (via) => via.DenominacionCompleta.toLowerCase() === filtro
      );

      if (coincidenciaExacta) {
        console.log('Coincidencia exacta encontrada:', coincidenciaExacta);
        this.seleccionarViaCatastro(coincidenciaExacta);
      }
    } else {
      this.viasCatastroFiltradas = [...this.viasCatastro];
    }

    console.log(
      'Filtrando vías:',
      filtro,
      'Resultados:',
      this.viasCatastroFiltradas.length
    );
  }

  // Método para cargar vías
  cargarViasCatastro(
    codigoProvincia: string | number,
    codigoMunicipio: string | number
  ) {
    console.log('Buscando vías para municipio:', codigoMunicipio);
    this.cargandoVias = true;

    // Usar el método getVias en lugar de getViasByMunicipio
    this.catastroService.getVias(codigoProvincia, codigoMunicipio).subscribe({
      next: (response) => {
        console.log('Vías cargadas:', response);
        if (response && response.d) {
          this.viasCatastro = response.d;
        } else {
          this.viasCatastro = response || [];
        }
        this.viasCatastroFiltradas = [...this.viasCatastro]; // Inicializar lista filtrada
        this.cargandoVias = false;

        // Mostrar el dropdown de vías
        this.mostrarVias = true;
      },
      error: (error) => {
        console.error('Error al cargar vías:', error);
        this.cargandoVias = false;
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No se pudieron cargar las vías',
          confirmButtonColor: '#d33',
        });
      },
    });
  }

  // Método para seleccionar una vía
  seleccionarViaCatastro(via: any) {
    this.selectedViaCatastro = via;

    // Actualizar el valor del campo de texto con el nombre de la vía seleccionada
    this.viaFilterControl.setValue(via.DenominacionCompleta, {
      emitEvent: false,
    });

    // Cerrar dropdown inmediatamente
    this.mostrarVias = false;

    // Actualizar la dirección completa con la provincia, municipio y vía seleccionados
    this.actualizarDireccionCompleta();

    //console.log('Vía seleccionada:', via.DenominacionCompleta, 'Código:', via.Codigo);

    // Asegurar que el dropdown se mantenga cerrado
    setTimeout(() => {
      this.mostrarVias = false;
    }, 50);
  }

  // Método para actualizar la dirección completa
  actualizarDireccionCompleta(index: number = 0) {
    let direccionCompleta = '';
    let via = '';

    // Procesar la vía si está seleccionada
    if (this.selectedViaCatastro) {
      const denominacionCompleta =
        this.selectedViaCatastro.DenominacionCompleta;

      // Verificar si la vía tiene un tipo entre paréntesis (ej: "VIA 1 PRUEBA (CALLE)")
      const tipoViaMatch = denominacionCompleta.match(/(.+)\s+\(([^)]+)\)/);

      if (tipoViaMatch) {
        // Si hay coincidencia, extraer el nombre de la vía y el tipo
        const nombreVia = tipoViaMatch[1].trim();
        const tipoVia = tipoViaMatch[2].trim();
        // Reorganizar como "TIPO NOMBRE" (ej: "CALLE VIA 1 PRUEBA")
        via = tipoVia + ' ' + nombreVia;
      } else {
        // Si no hay paréntesis, usar la denominación completa
        via = denominacionCompleta;
      }

      direccionCompleta = via;
    }

    // Agregar el número si existe (con "N" antes)
    if (this.formData.clienteResidencial.numero) {
      if (direccionCompleta) {
        direccionCompleta += ' N° ' + this.formData.clienteResidencial.numero;
      } else {
        direccionCompleta = 'N° ' + this.formData.clienteResidencial.numero;
      }
    }

    // Agregar el bloque si existe (con "BLOQUE" antes)
    if (this.formData.clienteResidencial.bloque) {
      if (direccionCompleta) {
        direccionCompleta +=
          ' BLOQUE ' + this.formData.clienteResidencial.bloque;
      } else {
        direccionCompleta = 'BLOQUE ' + this.formData.clienteResidencial.bloque;
      }
    }

    // Agregar la escalera si existe (con "ESCALERA" antes)
    if (this.formData.clienteResidencial.escalera) {
      if (direccionCompleta) {
        direccionCompleta +=
          ' ESCALERA ' + this.formData.clienteResidencial.escalera;
      } else {
        direccionCompleta =
          'ESCALERA ' + this.formData.clienteResidencial.escalera;
      }
    }

    // Agregar la planta si existe (con "PLANTA" antes)
    if (this.formData.clienteResidencial.planta) {
      if (direccionCompleta) {
        direccionCompleta +=
          ' PLANTA ' + this.formData.clienteResidencial.planta;
      } else {
        direccionCompleta = 'PLANTA ' + this.formData.clienteResidencial.planta;
      }
    }

    // Agregar la puerta si existe (con "PUERTA" antes)
    if (this.formData.clienteResidencial.puerta) {
      if (direccionCompleta) {
        direccionCompleta +=
          ' PUERTA ' + this.formData.clienteResidencial.puerta;
      } else {
        direccionCompleta = 'PUERTA ' + this.formData.clienteResidencial.puerta;
      }
    }

    // Agregar el municipio si está seleccionado
    if (this.selectedMunicipioCatastro) {
      if (direccionCompleta) {
        direccionCompleta += ', ' + this.selectedMunicipioCatastro.Denominacion;
      } else {
        direccionCompleta = this.selectedMunicipioCatastro.Denominacion;
      }
    }

    // Agregar la provincia si está seleccionada
    if (this.selectedProvinciaCatastro) {
      if (direccionCompleta) {
        direccionCompleta += ', ' + this.selectedProvinciaCatastro.Denominacion;
      } else {
        direccionCompleta = this.selectedProvinciaCatastro.Denominacion;
      }
    }

    // Agregar el código postal si existe (sin "CP:")
    const codigoPostal = this.formData.clienteResidencial.codigoPostal?.trim();
    if (codigoPostal) {
      if (direccionCompleta) {
        direccionCompleta += ', ' + codigoPostal;
      } else {
        direccionCompleta = codigoPostal;
      }
    }

    // Actualizar el campo de dirección
    this.formData.clienteResidencial.direccion = direccionCompleta;
  }

  // Métodos para manejar los dropdowns
  toggleProvinciasDropdown(event: Event) {
    event.stopPropagation();
    event.preventDefault();

    // Limpiar los campos de provincia, municipio y vía
    this.provinciaFilterControl.setValue('', { emitEvent: false });
    this.municipioFilterControl.setValue('', { emitEvent: false });
    this.viaFilterControl.setValue('', { emitEvent: false });

    // Limpiar las selecciones
    this.selectedProvinciaCatastro = null;
    this.selectedMunicipioCatastro = null;
    this.selectedViaCatastro = null;

    // Limpiar los valores en el formulario
    this.formData.clienteResidencial.provincia = '';
    this.formData.clienteResidencial.distrito = '';

    // Limpiar las listas de municipios y vías
    this.municipiosCatastro = [];
    this.viasCatastro = [];
    this.municipiosCatastroFiltrados = [];
    this.viasCatastroFiltradas = [];

    // Cerrar otros dropdowns
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    // Siempre mostrar el dropdown de provincias
    this.mostrarProvincias = true;

    // Siempre cargar las provincias al hacer clic
    this.cargandoProvincias = true;

    // Pequeño retraso para evitar que aparezca el autocompletador del navegador
    setTimeout(() => {
      this.catastroService
        .getProvincias(this.provinciaFilterControl.value || '')
        .subscribe({
          next: (response) => {
            if (response && response.d) {
              this.provinciasCatastro = response.d;
            } else {
              this.provinciasCatastro = response || [];
            }
            this.provinciasCatastroFiltradas = [...this.provinciasCatastro]; // Inicializar lista filtrada
            this.cargandoProvincias = false;
          },
          error: () => {
            this.cargandoProvincias = false;
          },
        });
    }, 10);

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta();
  }

  toggleMunicipiosDropdown(event: Event) {
    event.stopPropagation();
    event.preventDefault();

    // Si no hay provincia seleccionada, no hacer nada
    if (!this.selectedProvinciaCatastro) {
      return;
    }

    // Limpiar los campos de municipio y vía
    this.municipioFilterControl.setValue('', { emitEvent: false });
    this.viaFilterControl.setValue('', { emitEvent: false });

    // Limpiar las selecciones
    this.selectedMunicipioCatastro = null;
    this.selectedViaCatastro = null;

    // Limpiar los valores en el formulario
    this.formData.clienteResidencial.distrito = '';

    // Limpiar las listas de vías
    this.viasCatastro = [];
    this.viasCatastroFiltradas = [];

    // Cerrar otros dropdowns
    this.mostrarProvincias = false;
    this.mostrarVias = false;

    // Siempre mostrar el dropdown de municipios
    this.mostrarMunicipios = true;

    // Siempre cargar los municipios al hacer clic
    this.cargandoMunicipios = true;

    // Pequeño retraso para evitar que aparezca el autocompletador del navegador
    setTimeout(() => {
      this.catastroService
        .getMunicipios(this.selectedProvinciaCatastro.Codigo)
        .subscribe({
          next: (response) => {
            if (response && response.d) {
              this.municipiosCatastro = response.d;
            } else {
              this.municipiosCatastro = response || [];
            }
            this.municipiosCatastroFiltrados = [...this.municipiosCatastro]; // Inicializar lista filtrada
            this.cargandoMunicipios = false;
          },
          error: () => {
            this.cargandoMunicipios = false;
          },
        });
    }, 10);

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta();
  }

  toggleViasDropdown(event: Event) {
    event.stopPropagation();
    event.preventDefault();

    // Si no hay municipio seleccionado, no hacer nada
    if (!this.selectedProvinciaCatastro || !this.selectedMunicipioCatastro) {
      return;
    }

    // Limpiar el campo de vía
    this.viaFilterControl.setValue('', { emitEvent: false });

    // Limpiar la selección
    this.selectedViaCatastro = null;

    // Cerrar otros dropdowns
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;

    // Siempre mostrar el dropdown de vías
    this.mostrarVias = true;

    // Siempre cargar las vías al hacer clic
    this.cargandoVias = true;

    // Pequeño retraso para evitar que aparezca el autocompletador del navegador
    setTimeout(() => {
      this.catastroService
        .getVias(
          this.selectedProvinciaCatastro.Codigo,
          this.selectedMunicipioCatastro.Codigo,
          this.viaFilterControl.value || ''
        )
        .subscribe({
          next: (response) => {
            if (response && response.d) {
              this.viasCatastro = response.d;
            } else {
              this.viasCatastro = response || [];
            }
            this.viasCatastroFiltradas = [...this.viasCatastro]; // Inicializar lista filtrada
            this.cargandoVias = false;
          },
          error: () => {
            this.cargandoVias = false;
          },
        });
    }, 10);

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta();
  }

  // Manejo de permanencia
  onPermanenciaChange(value: string) {
    if (value === 'si') {
      this.showDatePicker = true;
      this.formData.clienteResidencial.permanencia = '';
    } else if (value === 'No tiene permanencia') {
      this.showDatePicker = false;
      this.formData.clienteResidencial.permanencia = 'No tiene permanencia';
    } else if (this.isDateFormat(value)) {
      this.showDatePicker = false;
      this.formData.clienteResidencial.permanencia = value;
    }
  }

  chosenMonthHandler(normalizedMonth: Date, datepicker: any) {
    const month = normalizedMonth.getMonth() + 1;
    const year = normalizedMonth.getFullYear();
    const formatted = (month < 10 ? '0' + month : month) + '/' + year;
    this.selectedPermanencia = formatted;
    this.formData.clienteResidencial.permanencia = formatted;
    datepicker.close();
    this.showDatePicker = false;
  }

  // Manejo de nacionalidad
  onNacionalidadChange(value: string) {
    const nacionalidadOtra = value === 'otra';
    if (nacionalidadOtra) {
      this.formData.clienteResidencial.nacionalidad = '';
    }
  }

  isDateFormat(value: string): boolean {
    return /^\d{2}\/\d{4}$/.test(value);
  }

  // Sign out
  onSignOut(): void {
    localStorage.removeItem('token');
    this.store.dispatch(new fromUser.SignOut());
    this.router.navigate(['/auth/login']);
  }

  isAdmin(): boolean {
    return this.userData?.role === 'ADMIN';
  }

  // Métodos para manejar la lista de móviles a portar
  addMovilAPortar() {
    this.formData.clienteResidencial.movilesAPortar.push('');
  }

  removeMovilAPortar(index: number) {
    this.formData.clienteResidencial.movilesAPortar.splice(index, 1);
  }

  hasMovilesAPortar(): boolean {
    return this.formData.clienteResidencial.movilesAPortar.length > 0;
  }

  trackByIndex(index: number, _item: any): number {
    // El guion bajo en _item indica que es un parámetro que no se utiliza
    return index;
  }

  validateNumberInput(event: KeyboardEvent) {
    // Usar key en lugar de charCode/keyCode (que están obsoletos)
    const key = event.key;
    // Permitir solo dígitos del 0-9
    if (
      !/^[0-9]$/.test(key) &&
      // Permitir teclas de control como backspace, delete, flechas, etc.
      !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(key)
    ) {
      event.preventDefault();
    }
  }

  validatePaste(event: ClipboardEvent) {
    const clipboardData = event.clipboardData?.getData('text') || '';
    if (!/^\d+$/.test(clipboardData)) {
      event.preventDefault();
    }
  }

  onMovilInput(event: Event, index: number) {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;
    value = value.replace(/\D/g, ''); // Solo números
    if (value.length > 9) {
      value = value.slice(0, 9);
    }
    this.formData.clienteResidencial.movilesAPortar[index] = value;
    inputElement.focus();
  }

  // Método para manejar cambios en el código postal
  onCodigoPostalInput(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;
    value = value.replace(/\D/g, ''); // Solo números
    if (value.length > 5) {
      value = value.slice(0, 5);
    }
    this.formData.clienteResidencial.codigoPostal = value;
    this.actualizarDireccionCompleta();
  }

  /**
   * Abre el mapa directamente con el último tipo utilizado
   */
  abrirMapaDirecto() {
    // Ocultar el panel hover
    this.showMapHoverPanel = false;

    // Detener el temporizador
    this.stopMapHoverTimer();

    // Usar el último tipo de mapa seleccionado
    this.verEnMapa(this.lastMapType);
  }

  /**
   * Abre un diálogo con el mapa seleccionado en lugar de redirigir a una nueva pestaña
   * @param mapTypePreseleccionado Tipo de mapa preseleccionado (opcional)
   */
  verEnMapa(mapTypePreseleccionado?: 'leaflet' | 'mapbox') {
    // Verificar que estén completos los campos de provincia, municipio y vía
    // Permitir abrir el mapa si tenemos los valores en el formulario, aunque no tengamos los objetos seleccionados
    const viaValue = this.selectedViaCatastro
      ? this.selectedViaCatastro.DenominacionCompleta
      : this.viaFilterControl.value;

    if (
      !this.formData.clienteResidencial.provincia ||
      !this.formData.clienteResidencial.distrito ||
      !viaValue
    ) {
      Swal.fire({
        icon: 'warning',
        title: 'Información incompleta',
        text: 'Debes seleccionar provincia, municipio y vía para ver el mapa',
        confirmButtonColor: '#3085d6',
      });
      return;
    }

    // Preparar los parámetros básicos para el mapa (comunes a ambos tipos)
    const queryParams: { [key: string]: string } = {
      provincia: this.formData.clienteResidencial.provincia,
      municipio: this.formData.clienteResidencial.distrito,
      via: this.selectedViaCatastro
        ? this.selectedViaCatastro.DenominacionCompleta
        : this.viaFilterControl.value,
      codigoPostal: this.formData.clienteResidencial.codigoPostal,
      // Añadir siempre los parámetros adicionales de dirección para ambos tipos de mapa
      numero: this.formData.clienteResidencial.numero || '',
      bloque: this.formData.clienteResidencial.bloque || '',
      escalera: this.formData.clienteResidencial.escalera || '',
      planta: this.formData.clienteResidencial.planta || '',
      puerta: this.formData.clienteResidencial.puerta || '',
    };

    // Si tenemos los códigos de provincia y municipio, los incluimos para la API del Catastro
    if (this.selectedProvinciaCatastro) {
      queryParams['codigoProvincia'] = this.selectedProvinciaCatastro.Codigo;
    }

    if (this.selectedMunicipioCatastro) {
      queryParams['codigoMunicipio'] = this.selectedMunicipioCatastro.Codigo;
    }

    // Si se proporcionó un tipo de mapa preseleccionado, usarlo directamente
    if (mapTypePreseleccionado) {
      this.abrirDialogoMapa(mapTypePreseleccionado, queryParams);
      return;
    }

    // Mostrar diálogo para elegir el tipo de mapa
    Swal.fire({
      title: 'Seleccionar tipo de mapa',
      text: 'Elige el tipo de mapa que deseas utilizar',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#2196f3',
      confirmButtonText: 'Mapa Leaflet',
      cancelButtonText: 'Mapa Mapbox',
      reverseButtons: true,
    }).then((result) => {
      // Determinar el tipo de mapa seleccionado
      let mapType: 'leaflet' | 'mapbox';

      if (result.isConfirmed) {
        // Usar Leaflet (Nominatim/Catastro)
        mapType = 'leaflet';
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        // Usar Mapbox
        mapType = 'mapbox';
      } else {
        // El usuario cerró el diálogo sin elegir
        return;
      }

      this.abrirDialogoMapa(mapType, queryParams);
    });
  }

  /**
   * Abre el diálogo del mapa con el tipo y parámetros especificados
   */
  private abrirDialogoMapa(mapType: 'leaflet' | 'mapbox', queryParams: any) {
    // Verificar si el tema oscuro está activo
    const isDarkTheme = document.body.classList.contains('dark-theme');

    // Abrir el diálogo con el mapa seleccionado
    const dialogRef = this.dialog.open(MapDialogComponent, {
      maxWidth: '100vw',
      maxHeight: '100vh',
      height: '100%',
      width: '100%',
      panelClass: ['fullscreen-dialog', isDarkTheme ? 'dark-theme' : ''],
      data: {
        mapType: mapType,
        queryParams: queryParams,
      },
    });

    // Suscribirse al evento de cierre del diálogo para recibir los datos actualizados
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Guardar el último tipo de mapa utilizado
        if (result.mapType) {
          this.lastMapType = result.mapType;
        }

        // Mostrar el panel hover después de cerrar el diálogo
        this.showMapHoverPanel = true;

        // Iniciar el temporizador para el panel hover
        this.startMapHoverTimer();

        // Actualizar los campos de dirección con los valores recibidos del diálogo
        this.formData.clienteResidencial.bloque =
          result.bloque || this.formData.clienteResidencial.bloque;
        this.formData.clienteResidencial.escalera =
          result.escalera || this.formData.clienteResidencial.escalera;
        this.formData.clienteResidencial.planta =
          result.planta || this.formData.clienteResidencial.planta;
        this.formData.clienteResidencial.puerta =
          result.puerta || this.formData.clienteResidencial.puerta;
        this.formData.clienteResidencial.numero =
          result.numero || this.formData.clienteResidencial.numero;

        // Actualizar el código postal si se recibió uno nuevo
        if (result.codigoPostal) {
          this.formData.clienteResidencial.codigoPostal = result.codigoPostal;
        }

        // Actualizar provincia, municipio y vía si se recibieron nuevos valores
        if (result.provincia) {
          this.formData.clienteResidencial.provincia = result.provincia;
          // Si cambia la provincia, actualizar el control de filtro y la selección
          this.provinciaFilterControl.setValue(result.provincia, {
            emitEvent: false,
          });
          // Buscar la provincia en la lista para actualizar la selección
          if (this.provinciasCatastro.length > 0) {
            this.selectedProvinciaCatastro = this.provinciasCatastro.find(
              (p) => p.Denominacion === result.provincia
            );
          }

          // Si no encontramos la provincia en la lista, crear un objeto personalizado
          if (!this.selectedProvinciaCatastro && result.provincia) {
            this.selectedProvinciaCatastro = {
              Denominacion: result.provincia,
              Codigo: '0', // Código ficticio
            };
          }
        }

        if (result.municipio) {
          this.formData.clienteResidencial.distrito = result.municipio;
          // Si cambia el municipio, actualizar el control de filtro y la selección
          this.municipioFilterControl.setValue(result.municipio, {
            emitEvent: false,
          });
          // Buscar el municipio en la lista para actualizar la selección
          if (this.municipiosCatastro.length > 0) {
            this.selectedMunicipioCatastro = this.municipiosCatastro.find(
              (m) => m.Denominacion === result.municipio
            );
          }

          // Si no encontramos el municipio en la lista, crear un objeto personalizado
          if (!this.selectedMunicipioCatastro && result.municipio) {
            this.selectedMunicipioCatastro = {
              Denominacion: result.municipio,
              Codigo: '0', // Código ficticio
            };
          }
        }

        if (result.via) {
          // Si cambia la vía, actualizar el control de filtro y la selección
          this.viaFilterControl.setValue(result.via, { emitEvent: false });
          // Buscar la vía en la lista para actualizar la selección
          if (this.viasCatastro.length > 0) {
            this.selectedViaCatastro = this.viasCatastro.find(
              (v) => v.DenominacionCompleta === result.via
            );
          }

          // Si no encontramos la vía en la lista, crear un objeto personalizado
          if (!this.selectedViaCatastro && result.via) {
            this.selectedViaCatastro = {
              DenominacionCompleta: result.via,
              Denominacion: result.via,
              Codigo: '0', // Código ficticio
              TipoVia: '', // Tipo de vía vacío
            };
          }
        }

        // Actualizar la dirección completa
        // Ahora siempre deberíamos tener los objetos seleccionados
        this.actualizarDireccionCompleta();

        // El panel hover se ocultará automáticamente cuando termine el temporizador
      }
    });
  }

  onSubmit() {
    // Validar que el número de agente esté presente
    if (!this.formData.clienteResidencial.numeroAgente?.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'El número de agente es un campo obligatorio',
        confirmButtonColor: '#d33',
      });
      return;
    }

    // Validar que el móvil de contacto esté presente
    if (!this.formData.clienteResidencial.movilContacto?.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'El móvil de contacto es un campo obligatorio',
        confirmButtonColor: '#d33',
      });
      return;
    }

    // Validar que el móvil de contacto tenga exactamente 9 dígitos
    if (
      this.formData.clienteResidencial.movilContacto.length !== 9 ||
      !/^\d{9}$/.test(this.formData.clienteResidencial.movilContacto)
    ) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'El móvil de contacto debe contener exactamente 9 dígitos',
        confirmButtonColor: '#d33',
      });
      return;
    }

    this.isSubmitting = true; // 🚫 Bloquea botón

    const movilesAPortarFiltrados =
      this.formData.clienteResidencial.movilesAPortar.filter(
        (numero) => numero.trim() !== ''
      );

    const payload = {
      clienteResidencial: {
        ...this.formData.clienteResidencial,
        movilesAPortar: movilesAPortarFiltrados,
      },
      usuarioId: this.userData?.id,
    };

    this.http
      .post(`${environment.url}api/cliente-promocion`, payload)
      .subscribe({
        next: () => {
          Swal.fire({
            icon: 'success',
            title: '¡Éxito!',
            text: 'Datos enviados correctamente',
            confirmButtonColor: '#3085d6',
          });
          const numeroAgente = this.formData.clienteResidencial.numeroAgente;

          // Limpiar completamente el formulario
          this.resetFormData(numeroAgente);

          // Asegurarse de que los campos de dirección estén limpios
          setTimeout(() => {
            // Limpiar los controles de formulario para provincia, municipio y vía
            this.provinciaFilterControl.setValue('', { emitEvent: false });
            this.municipioFilterControl.setValue('', { emitEvent: false });
            this.viaFilterControl.setValue('', { emitEvent: false });

            // Limpiar las selecciones de provincia, municipio y vía
            this.selectedProvinciaCatastro = null;
            this.selectedMunicipioCatastro = null;
            this.selectedViaCatastro = null;
          }, 100);

          sessionStorage.removeItem('pendingClienteData');
          this.isSubmitting = false; // ✅ Desbloquea botón
        },
        error: (error) => {
          this.isSubmitting = false; // ✅ Desbloquea incluso si hay error
          if (error.status === 401 || error.status === 500) {
            sessionStorage.setItem(
              'pendingClienteData',
              JSON.stringify(this.formData)
            );
            localStorage.removeItem('token');
            this.store.dispatch(new fromUser.SignOut());
            this.router.navigate(['/auth/login']);
            Swal.fire({
              icon: 'warning',
              title: 'Sesión expirada',
              text: 'Tu sesión ha expirado. Los datos se han guardado.',
              confirmButtonColor: '#3085d6',
            });
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Ocurrió un error al enviar los datos',
              confirmButtonColor: '#d33',
            });
          }
        },
      });
  }

  private resetFormData(numeroAgente: string) {
    // Guardar el número de agente
    this._numeroAgente = numeroAgente;

    // Limpiar los controles de formulario para provincia, municipio y vía
    this.provinciaFilterControl.setValue('', { emitEvent: false });
    this.municipioFilterControl.setValue('', { emitEvent: false });
    this.viaFilterControl.setValue('', { emitEvent: false });

    // Limpiar las selecciones de provincia, municipio y vía
    this.selectedProvinciaCatastro = null;
    this.selectedMunicipioCatastro = null;
    this.selectedViaCatastro = null;

    // Limpiar los arrays de datos
    this.provinciasCatastro = [];
    this.municipiosCatastro = [];
    this.viasCatastro = [];
    this.provinciasCatastroFiltradas = [];
    this.municipiosCatastroFiltrados = [];
    this.viasCatastroFiltradas = [];

    // Ocultar los dropdowns
    this.mostrarProvincias = false;
    this.mostrarMunicipios = false;
    this.mostrarVias = false;

    this.formData = {
      clienteResidencial: {
        movilContacto: '',
        campania: '',
        nombresApellidos: '',
        nifNie: '',
        tipoFibra: '',
        permanencia: '',
        promocion: '',
        fechaNacimiento: '',
        genero: '',
        numeroMoviles: '',
        planActual: '',
        codigoPostal: '',
        provincia: '',
        distrito: '',
        ciudad: '',
        direccion: '',
        // Nuevos campos para dirección interna
        numero: '',
        bloque: '',
        escalera: '',
        planta: '',
        puerta: '',
        fijoCompania: '',
        movilesAPortar: [],
        tipoPlan: '',
        icc: '',
        usuarioId: undefined,
        nacionalidad: '',
        correoElectronico: '',
        cuentaBancaria: '',
        autorizaSeguros: false,
        autorizaEnergias: false,
        ventaRealizada: false,
        deseaPromocionesLowi: false,
        observacion: '',
        numeroAgente: this._numeroAgente,
        estadoLlamada: '',
        titularDelServicio: '',
        futbol: '',
        tipoTecnologia: '',
        velocidad: '',
      },
    };

    if (this.clienteForm) {
      this.clienteForm.resetForm();
      // Asegurar que el número de agente se mantenga después del reset
      setTimeout(() => {
        this.formData.clienteResidencial.numeroAgente = this._numeroAgente;
      });
    }
  }

  /**
   * Inicia el temporizador para el panel hover del mapa
   */
  startMapHoverTimer(): void {
    // Reiniciar el tiempo restante
    this.mapHoverTimeRemaining = 10;

    // Limpiar cualquier temporizador existente
    if (this.mapHoverTimer) {
      clearInterval(this.mapHoverTimer);
    }

    // Iniciar un nuevo temporizador que se ejecuta cada segundo
    this.mapHoverTimer = setInterval(() => {
      this.mapHoverTimeRemaining--;

      // Si el tiempo llega a cero, ocultar el panel y detener el temporizador
      if (this.mapHoverTimeRemaining <= 0) {
        this.showMapHoverPanel = false;
        this.stopMapHoverTimer();
      }
    }, 1000);
  }

  /**
   * Detiene el temporizador para el panel hover del mapa
   */
  stopMapHoverTimer(): void {
    if (this.mapHoverTimer) {
      clearInterval(this.mapHoverTimer);
      this.mapHoverTimer = null;
    }
  }

  /**
   * Reinicia el temporizador para el panel hover del mapa
   * Se llama cuando el usuario interactúa con el panel
   */
  resetMapHoverTimer(): void {
    this.mapHoverTimeRemaining = 10;
  }

  /**
   * Método del ciclo de vida que se ejecuta cuando se destruye el componente
   * Limpia los temporizadores para evitar fugas de memoria
   */
  ngOnDestroy(): void {
    // Detener el temporizador del panel hover
    this.stopMapHoverTimer();
  }

  // Método para actualizar la provincia cuando el usuario escribe directamente
  actualizarProvinciaManual(index: number = 0, event?: Event): void {
    // Asegurarse de que el índice sea válido
    if (index < 0) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener el valor del campo, ya sea del evento o del control
    let valor = '';
    if (event) {
      const inputElement = event.target as HTMLInputElement;
      valor = inputElement.value;
    } else {
      valor = this.provinciaFilterControl.value || '';
    }

    // Actualizar la provincia en el objeto de datos
    this.formData.clienteResidencial.provincia = valor;

    // Limpiar la selección de provincia del catastro
    this.selectedProvinciaCatastro = null;

    // Crear un objeto personalizado para la provincia
    if (valor) {
      this.selectedProvinciaCatastro = {
        Denominacion: valor,
        Codigo: '0', // Código ficticio
      };
    }

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta();
  }

  // Método para actualizar el municipio cuando el usuario escribe directamente
  actualizarMunicipioManual(index: number = 0, event?: Event): void {
    // Asegurarse de que el índice sea válido
    if (index < 0) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener el valor del campo, ya sea del evento o del control
    let valor = '';
    if (event) {
      const inputElement = event.target as HTMLInputElement;
      valor = inputElement.value;
    } else {
      valor = this.municipioFilterControl.value || '';
    }

    // Actualizar el municipio en el objeto de datos
    this.formData.clienteResidencial.distrito = valor;

    // Limpiar la selección de municipio del catastro
    this.selectedMunicipioCatastro = null;

    // Crear un objeto personalizado para el municipio
    if (valor) {
      this.selectedMunicipioCatastro = {
        Denominacion: valor,
        Codigo: '0', // Código ficticio
      };
    }

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta();
  }

  // Método para actualizar la vía cuando el usuario escribe directamente
  actualizarViaManual(index: number = 0, event?: Event): void {
    // Asegurarse de que el índice sea válido
    if (index < 0) {
      console.warn('Índice de dirección inválido:', index);
      return;
    }

    // Obtener el valor del campo, ya sea del evento o del control
    let valor = '';
    if (event) {
      const inputElement = event.target as HTMLInputElement;
      valor = inputElement.value;
    } else {
      valor = this.viaFilterControl.value || '';
    }

    // Actualizar la vía en el objeto de datos
    // Como no existe una propiedad 'via' en formData.clienteResidencial,
    // creamos un objeto personalizado para la vía
    this.selectedViaCatastro = null;
    if (valor) {
      this.selectedViaCatastro = {
        DenominacionCompleta: valor,
        Denominacion: valor,
        Codigo: '0', // Código ficticio
        TipoVia: '', // Tipo de vía vacío
      };
    }

    // Actualizar la dirección completa
    this.actualizarDireccionCompleta();
  }

  // Métodos para el filtro de operadores

  /**
   * Filtra la lista de operadores según el texto ingresado
   */
  filtrarOperadores(filtro: string): void {
    if (!filtro) {
      this.operadoresFiltrados = [...this.operadores];
    } else {
      this.operadoresFiltrados = this.operadores.filter((operador) =>
        operador.toLowerCase().includes(filtro.toLowerCase())
      );
    }
    this.mostrarOperadores = true;
  }

  /**
   * Maneja el evento de clic en el input de operadores
   */
  toggleOperadoresDropdown(event: Event): void {
    event.stopPropagation();
    this.mostrarOperadores = !this.mostrarOperadores;
    if (this.mostrarOperadores) {
      this.operadoresFiltrados = [...this.operadores];
    }
  }

  /**
   * Maneja el evento de input en el campo de operadores
   */
  onOperadorInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const valor = inputElement.value;

    // Filtrar operadores
    this.filtrarOperadores(valor);

    // Verificar si el valor actual es válido
    const operadorExacto = this.operadores.find(
      (op) => op.toLowerCase() === valor.toLowerCase()
    );

    if (operadorExacto) {
      // Si encuentra una coincidencia exacta, actualizar el formulario
      this.formData.clienteResidencial.campania = operadorExacto;
      this.operadorValido = true;
    } else {
      // Si no encuentra coincidencia exacta, limpiar el valor del formulario
      // pero mantener el texto en el input para que el usuario pueda seguir buscando
      this.formData.clienteResidencial.campania = '';
      this.operadorValido = valor === ''; // Solo es válido si está vacío
    }
  }

  /**
   * Maneja el evento de blur (cuando el usuario sale del campo)
   */
  onOperadorBlur(): void {
    const valorActual = this.operadorFilterControl.value || '';

    // Verificar si el valor actual es un operador válido
    const operadorValidoEncontrado = this.operadores.find(
      (op) => op.toLowerCase() === valorActual.toLowerCase()
    );

    if (operadorValidoEncontrado) {
      // Si es válido, asegurar que esté correctamente formateado
      this.operadorFilterControl.setValue(operadorValidoEncontrado);
      this.formData.clienteResidencial.campania = operadorValidoEncontrado;
      this.operadorValido = true;
    } else {
      // Si no es válido, limpiar el campo
      this.operadorFilterControl.setValue('');
      this.formData.clienteResidencial.campania = '';
      this.operadorValido = true; // Resetear a válido cuando está vacío
    }

    // Cerrar el dropdown
    this.mostrarOperadores = false;
  }

  /**
   * Selecciona un operador de la lista filtrada
   */
  seleccionarOperador(operador: string): void {
    this.formData.clienteResidencial.campania = operador;
    this.operadorFilterControl.setValue(operador);
    this.operadorValido = true;
    this.mostrarOperadores = false;
  }
}
