# 🎤 Cómo Usar el Modal de Transcripción

## ✨ Ejemplo Súper Simple

Copia y pega este código en cualquier componente donde quieras agregar el botón de transcripción:

### 1. En tu componente TypeScript:

```typescript
import { Component } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranscriptionDialogComponent, TranscriptionDialogData } from './path/to/transcription-dialog.component';

@Component({
  selector: 'app-tu-componente',
  template: `
    <!-- Tu HTML aquí -->
    
    <!-- Botón para abrir transcripción -->
    <button 
      type="button"
      mat-raised-button 
      color="primary" 
      (click)="abrirTranscripcion()">
      <mat-icon>record_voice_over</mat-icon>
      Transcribir Audio
    </button>
  `
})
export class TuComponente {

  constructor(private dialog: MatDialog) {}

  abrirTranscripcion(): void {
    // Configurar el modal
    const datos: TranscriptionDialogData = {
      allowFileUpload: true, // ✅ Esto habilita la subida de archivos
      // Opcional: datos del cliente
      cliente: {
        nombres: 'Nombre',
        apellidos: 'Apellido'
      },
      numeroMovil: '123456789'
    };

    // Abrir el modal
    const modalRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '95vw',
      maxWidth: '1000px',
      height: '95vh',
      maxHeight: '900px',
      data: datos
    });

    // Cuando se cierre el modal
    modalRef.afterClosed().subscribe(resultado => {
      if (resultado && resultado.success) {
        console.log('✅ ¡Transcripción exitosa!', resultado);
        alert('¡Transcripción completada!');
      }
    });
  }
}
```

### 2. Asegúrate de importar en tu módulo:

```typescript
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@NgModule({
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    // ... otros imports
  ]
})
```

## 🚀 ¡Eso es todo!

Con este código:

1. **Se abre el modal** cuando haces clic en el botón
2. **Aparece la zona de subida** donde puedes arrastrar archivos o hacer clic para seleccionar
3. **Soporta múltiples formatos**: MP3, WAV, M4A, OGG, FLAC
4. **Máximo 100MB** por archivo
5. **Transcripción automática** con IA
6. **Análisis de emociones** incluido
7. **Se guarda automáticamente** en el cliente (si proporcionas numeroMovil)

## 🎯 Variaciones del Botón

### Botón Flotante:
```html
<button mat-fab color="primary" (click)="abrirTranscripcion()">
  <mat-icon>mic</mat-icon>
</button>
```

### En un Menú:
```html
<button mat-menu-item (click)="abrirTranscripcion()">
  <mat-icon>record_voice_over</mat-icon>
  Transcribir Audio
</button>
```

### Con Icono Diferente:
```html
<button mat-raised-button color="accent" (click)="abrirTranscripcion()">
  <mat-icon>audiotrack</mat-icon>
  Subir Audio
</button>
```

## 📋 Lo que Incluye el Modal

- ✅ **Zona de drag & drop** para archivos
- ✅ **Botón de selección** de archivos
- ✅ **Validación de formatos** automática
- ✅ **Validación de tamaño** (máx 100MB)
- ✅ **Configuración de transcripción** (modelo, idioma, etc.)
- ✅ **Barra de progreso** durante el proceso
- ✅ **Resultados detallados** con análisis de emociones
- ✅ **Copia al portapapeles** del texto transcrito
- ✅ **Guardado automático** en el cliente

## 🔧 Personalización

Puedes personalizar el modal cambiando los datos:

```typescript
const datos: TranscriptionDialogData = {
  allowFileUpload: true,
  cliente: {
    nombres: 'Juan',
    apellidos: 'Pérez'
  },
  numeroMovil: '987654321'
};
```

¡Y listo! 🎉 Ya tienes transcripción de audio con IA en tu aplicación.
