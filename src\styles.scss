/* You can add global styles to this file, and also import other style files */
@import "@angular/material/prebuilt-themes/indigo-pink.css";

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos personalizados para Material Angular en modo oscuro ahora están integrados directamente */
html,
body {
  height: 100%;
  margin: 0 auto;
  padding: 0;
}

/* ya lo tienes: quita padding/margin y forzamos full‐screen */
.fullscreen-dialog .mat-dialog-container {
  padding: 0 !important;
  margin: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  overflow: hidden !important;
}

/* Eliminar padding para componentes de curso */
.curso-dialog .mat-dialog-container {
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

/* AHORA forzamos que la imagen llene el contenedor sin deformarse */
.fullscreen-dialog img {
  display: block;
  width: 100vw !important;
  height: 100vh !important;
  object-fit: contain;
}
