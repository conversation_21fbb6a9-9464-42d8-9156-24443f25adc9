import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@src/environments/environment';

export interface LogData {
  str_request: string;
  str_modulo: string;
  str_operacion: string;
  str_tabla: string;
  str_pagina: string;
  int_visitas: number;
  int_idRegistro?: number;
  json_valorAnterior?: any;
  json_valorNuevo?: any;
  idUsuario: number;
}

@Injectable({
  providedIn: 'root'
})
export class LogService {
 
  constructor(private http: HttpClient) {}

  private crearLog(data: Partial<LogData>): Observable<any> {
    const userStr = localStorage.getItem('user');
    const user = userStr ? JSON.parse(userStr) : null;

    const logData: LogData = {
      str_request: data.str_request || window.location.href,
      str_modulo: data.str_modulo || '',
      str_operacion: data.str_operacion || '',
      str_tabla: data.str_tabla || '',
      str_pagina: data.str_pagina || window.location.pathname,
      int_visitas: data.int_visitas || 1,
      int_idRegistro: data.int_idRegistro,
      json_valorAnterior: data.json_valorAnterior,
      json_valorNuevo: data.json_valorNuevo,
      idUsuario: data.idUsuario || (user?.id || 0)
    };

    return this.http.post<any>(`${environment.urlVentas}logs/crear/`, logData);
  }

  logEliminacion(modulo: string, tabla: string, idRegistro: number, codigoVenta: string): Observable<any> {
    return this.crearLog({
      str_modulo: modulo,
      str_operacion: 'ELIMINAR',
      str_tabla: tabla,
      int_idRegistro: idRegistro,
      json_valorAnterior:  codigoVenta 
    });
  }

  logCreacion(modulo: string, tabla: string, idRegistro: number, codigoVenta: string): Observable<any> {
    return this.crearLog({
      str_modulo: modulo,
      str_operacion: 'CREAR',
      str_tabla: tabla,
      int_idRegistro: idRegistro,
      json_valorNuevo:  codigoVenta
    });
  }

  logEdicion(modulo: string, tabla: string, idRegistro: number, json_valorAnterior: string, json_valorNuevo: string): Observable<any> {
    return this.crearLog({
      str_modulo: modulo,
      str_operacion: 'EDITAR',
      str_tabla: tabla,
      int_idRegistro: idRegistro,
      json_valorAnterior: json_valorAnterior,
      json_valorNuevo: json_valorNuevo
    });
  }

  logObservacion(modulo: string, tabla: string, idRegistro: number, codigoVenta: string, observacion: string): Observable<any> {
    return this.crearLog({
      str_modulo: modulo,
      str_operacion: 'INGRESÓ OBSERVACION',
      str_tabla: tabla,
      int_idRegistro: idRegistro,
      json_valorNuevo: { 
        codigoVenta: codigoVenta,
        observacion: observacion 
      }
    });
  }

  logAsignacionBackoffice(modulo: string, tabla: string, idRegistro: number, codigoVenta: string, backofficeId: number): Observable<any> {
    return this.crearLog({
      str_modulo: modulo,
      str_operacion: 'ASIGNAR_BACKOFFICE',
      str_tabla: tabla,
      int_idRegistro: idRegistro,
      json_valorNuevo: { 
        codigoVenta: codigoVenta,
        backofficeId: backofficeId 
      }
    });
  }
}
