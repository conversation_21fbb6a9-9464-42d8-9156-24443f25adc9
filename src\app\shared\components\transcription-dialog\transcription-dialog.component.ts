import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranscriptionService, TranscriptionRequest, TranscriptionResponse } from '../../../services/transcription.service';
import { GoogleDriveFile, GoogleDriveService } from '../../../services/google-drive.service';
import { TranscriptionSaveService, TranscriptionSaveRequest, TranscriptionVerificationResponse } from '../../../services/transcription-save.service';

export interface TranscriptionDialogData {
  file?: GoogleDriveFile;
  cliente?: {
    nombres: string;
    apellidos: string;
  };
  numeroMovil?: string;
  allowFileUpload?: boolean; // Nueva propiedad para permitir subida de archivos
}

@Component({
  selector: 'app-transcription-dialog',
  templateUrl: './transcription-dialog.component.html'
})
export class TranscriptionDialogComponent implements OnInit {
  
  // Estado del proceso
  isProcessing = false;
  currentStep: 'config' | 'downloading' | 'transcribing' | 'completed' | 'error' = 'config';
  progress = 0;
  statusMessage = '';

  // Estado de verificación de transcripción
  isVerifying = false;
  verificationResult: TranscriptionVerificationResponse | null = null;
  showExistingTranscriptionWarning = false;

  // Estado de subida de archivos
  uploadedFile: File | null = null;
  isDragOver = false;
  allowFileUpload = false;

  // Configuración de transcripción
  transcriptionConfig = {
    whisper_model: 'base' as 'tiny' | 'base' | 'small' | 'medium' | 'large' | 'turbo',
    device: 'cpu' as 'cpu' | 'gpu',
    target_language: 'es',
    call_type: 'inbound' as 'inbound' | 'outbound' | 'internal',
    agent_id: '',
    call_datetime: new Date().toISOString().slice(0, 16) // YYYY-MM-DDTHH:mm
  };

  // Resultado de la transcripción
  transcriptionResult: TranscriptionResponse | null = null;

  // Propiedades para mostrar información adicional
  showAdvancedInfo = false;

  // Opciones disponibles
  whisperModels = [
    { value: 'tiny', label: 'Tiny - Muy rápido, precisión básica (~39MB)', speed: '⚡⚡⚡', accuracy: '⭐' },
    { value: 'base', label: 'Base - Balanceado, buena precisión (~74MB) ⭐ Recomendado', speed: '⚡⚡', accuracy: '⭐⭐' },
    { value: 'small', label: 'Small - Buena calidad (~244MB)', speed: '⚡', accuracy: '⭐⭐⭐' },
    { value: 'medium', label: 'Medium - Alta precisión (~769MB)', speed: '⚡', accuracy: '⭐⭐⭐⭐' },
    { value: 'large', label: 'Large - Máxima precisión (~1550MB)', speed: '⚡', accuracy: '⭐⭐⭐⭐⭐' },
    { value: 'turbo', label: 'Turbo - Rápido y preciso (~809MB) ⭐ Mejor balance', speed: '⚡⚡⚡', accuracy: '⭐⭐⭐⭐' }
  ];

  devices = [
    { value: 'cpu', label: 'CPU - Compatibilidad total, más lento' },
    { value: 'gpu', label: 'GPU - Más rápido (requiere GPU NVIDIA con CUDA)' }
  ];

  callTypes = [
    { value: 'inbound', label: 'Entrante - Llamada recibida' },
    { value: 'outbound', label: 'Saliente - Llamada realizada' },
    { value: 'internal', label: 'Interna - Llamada entre agentes' }
  ];

  constructor(
    public dialogRef: MatDialogRef<TranscriptionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TranscriptionDialogData,
    private transcriptionService: TranscriptionService,
    private googleDriveService: GoogleDriveService,
    private transcriptionSaveService: TranscriptionSaveService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Configurar modo de subida de archivos
    this.allowFileUpload = this.data.allowFileUpload || false;

    // Configurar valores iniciales basados en los datos del cliente
    if (this.data.numeroMovil) {
      this.transcriptionConfig.agent_id = this.data.numeroMovil;
      // Verificar si el cliente ya tiene transcripción
      this.verificarTranscripcionExistente();
    }
  }

  /**
   * Verifica si el cliente ya tiene una transcripción registrada
   */
  verificarTranscripcionExistente(): void {
    if (!this.data.numeroMovil) {
      return;
    }

    this.isVerifying = true;
    this.statusMessage = 'Verificando transcripciones existentes...';

    this.transcriptionSaveService.verificarTranscripcion(this.data.numeroMovil).subscribe({
      next: (response) => {
        console.log('=== RESULTADO DE VERIFICACIÓN ===');
        console.log('Response:', response);

        this.verificationResult = response;
        this.isVerifying = false;

        if (response.tieneTranscripcion) {
          this.showExistingTranscriptionWarning = true;
          this.statusMessage = response.mensaje;

          // Mostrar mensaje informativo
          this.snackBar.open(
            `⚠️ ${response.mensaje}`,
            'ENTENDIDO',
            {
              duration: 8000,
              panelClass: ['warning-snackbar'],
              horizontalPosition: 'center',
              verticalPosition: 'top'
            }
          );
        } else {
          this.statusMessage = 'Listo para transcribir';
        }
      },
      error: (error) => {
        console.error('Error al verificar transcripción:', error);
        this.isVerifying = false;
        this.statusMessage = 'Error al verificar transcripción existente';

        // No bloquear el proceso si hay error en la verificación
        this.showError('No se pudo verificar si existe una transcripción previa');
      }
    });
  }

  /**
   * Inicia el proceso de transcripción
   */
  async startTranscription(): Promise<void> {
    try {
      // Validar que hay un archivo (de Google Drive o subido)
      if (!this.data.file && !this.uploadedFile) {
        this.snackBar.open('Por favor selecciona un archivo de audio', 'Cerrar', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
        return;
      }

      // Si ya existe una transcripción, confirmar antes de proceder
      if (this.verificationResult?.tieneTranscripcion) {
        const confirmed = await this.confirmOverwriteTranscription();
        if (!confirmed) {
          return; // Usuario canceló
        }
      }

      this.isProcessing = true;
      this.currentStep = 'transcribing';
      this.progress = 10;

      let audioFile: File;

      if (this.uploadedFile) {
        // Usar archivo subido directamente
        this.statusMessage = 'Preparando archivo subido...';
        audioFile = this.uploadedFile;
        this.progress = 25;
      } else {
        // Descargar archivo desde Google Drive
        this.statusMessage = 'Descargando archivo desde Google Drive...';
        audioFile = await this.createFileFromGoogleDrive();
        this.progress = 25;
      }

      this.progress = 25;
      this.statusMessage = 'Enviando archivo al servidor de transcripción...';

      // Preparar la solicitud de transcripción
      const request: TranscriptionRequest = {
        audio_file: audioFile,
        whisper_model: this.transcriptionConfig.whisper_model,
        device: this.transcriptionConfig.device,
        target_language: this.transcriptionConfig.target_language,
        call_id: this.generateCallId(),
        call_type: this.transcriptionConfig.call_type,
        caller_phone: this.data.numeroMovil,
        agent_id: this.transcriptionConfig.agent_id,
        call_datetime: this.transcriptionConfig.call_datetime
      };

      // LOG DETALLADO: Datos que se envían al API
      console.log('=== DATOS ENVIADOS AL API DE TRANSCRIPCIÓN ===');
      console.log('URL del API:', 'https://apisozarusac.com/BackendTranscriptor/api/transcribe/');

      if (this.uploadedFile) {
        console.log('Archivo subido localmente:', {
          name: this.uploadedFile.name,
          size: this.uploadedFile.size,
          type: this.uploadedFile.type,
          lastModified: this.uploadedFile.lastModified
        });
      } else if (this.data.file) {
        console.log('Archivo seleccionado desde Google Drive:', {
          name: this.data.file.name,
          id: this.data.file.id,
          mimeType: this.data.file.mimeType,
          size: this.data.file.size,
          webViewLink: this.data.file.webViewLink
        });
      }

      console.log('Archivo final para envío:', {
        name: audioFile.name,
        size: audioFile.size,
        type: audioFile.type,
        lastModified: audioFile.lastModified
      });
      console.log('Request completo (sin archivo):', {
        whisper_model: request.whisper_model,
        device: request.device,
        target_language: request.target_language,
        call_id: request.call_id,
        call_type: request.call_type,
        caller_phone: request.caller_phone,
        agent_id: request.agent_id,
        call_datetime: request.call_datetime
      });
      console.log('Configuración actual:', JSON.stringify(this.transcriptionConfig, null, 2));
      console.log('===============================================');

      // Validar la solicitud
      const validation = this.transcriptionService.validateTranscriptionRequest(request);
      if (!validation.valid) {
        console.error('=== ERROR DE VALIDACIÓN ===');
        console.error('Errores:', validation.errors);
        console.error('==========================');
        throw new Error(validation.errors.join(', '));
      }

      this.progress = 50;
      this.statusMessage = 'Transcribiendo audio con IA... Esto puede tomar varios minutos.';

      // Realizar la transcripción - ESPERAR RESPUESTA REAL DEL SERVIDOR
      this.transcriptionService.transcribeAudio(request).subscribe({
        next: (response) => {
          console.log('=== RESPUESTA EXITOSA DEL API ===');
          console.log('Response completa:', JSON.stringify(response, null, 2));
          console.log('================================');

          // Guardar la transcripción en el cliente residencial
          this.transcriptionResult = response;
          const callId = request.call_id || this.generateCallId();
          this.saveTranscriptionToClient(response, callId);
        },
        error: (error) => {
          console.error('=== ERROR EN TRANSCRIPCIÓN ===');
          console.error('Error completo:', error);
          console.error('Status:', error.status);
          console.error('Status Text:', error.statusText);
          console.error('URL:', error.url);
          console.error('Headers:', error.headers);

          if (error.error) {
            console.error('Error body:', JSON.stringify(error.error, null, 2));
          }

          // Intentar mostrar el mensaje de error específico del servidor
          let errorMessage = 'Error al procesar la transcripción. Por favor, intente nuevamente.';
          if (error.error && error.error.error) {
            errorMessage = error.error.error;
            if (error.error.recommendation) {
              errorMessage += ` - ${error.error.recommendation}`;
            }

            // Si es un error de Numpy, dar sugerencias específicas
            if (error.error.error.includes('Numpy is not available')) {
              errorMessage += '\n\nSugerencias:\n';
              errorMessage += '• El servidor tiene problemas con las dependencias de Python\n';
              errorMessage += '• Contacte al administrador del sistema para verificar la instalación de Numpy\n';
              errorMessage += '• Intente nuevamente más tarde cuando el servidor esté funcionando correctamente';
            }
          } else if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          console.error('Mensaje de error final:', errorMessage);
          console.error('=============================');

          this.currentStep = 'error';
          this.isProcessing = false;
          this.statusMessage = errorMessage;
          this.showError('Error al procesar la transcripción');
        }
      });

    } catch (error) {
      console.error('Error:', error);
      this.currentStep = 'error';
      this.isProcessing = false;
      this.statusMessage = 'Error: ' + (error as Error).message;
      this.showError('Error al iniciar la transcripción');
    }
  }



  /**
   * Descarga el archivo real desde Google Drive usando el servicio
   */
  private async createFileFromGoogleDrive(): Promise<File> {
    if (!this.data.file) {
      throw new Error('No hay archivo de Google Drive disponible');
    }

    return new Promise((resolve, reject) => {
      console.log('=== DESCARGANDO ARCHIVO USANDO SERVICIO ===');
      console.log('File ID:', this.data.file!.id);
      console.log('File name:', this.data.file!.name);

      this.googleDriveService.downloadFile(this.data.file!.id).subscribe({
        next: (blob) => {
          console.log('Archivo descargado desde servicio:', {
            size: blob.size,
            type: blob.type
          });

          // Crear el archivo con el blob descargado
          const file = new File([blob], this.data.file!.name, {
            type: this.data.file!.mimeType || 'audio/mpeg'
          });

          console.log('Archivo final creado:', {
            name: file.name,
            size: file.size,
            type: file.type
          });
          console.log('==========================================');

          resolve(file);
        },
        error: (error) => {
          console.error('Error descargando archivo desde servicio:', error);
          reject(new Error(`No se pudo descargar el archivo: ${error.message || error}`));
        }
      });
    });
  }

  /**
   * Genera un ID único para la llamada
   */
  private generateCallId(): string {
    return this.transcriptionService.generateCallId(this.data.numeroMovil);
  }

  /**
   * Obtiene el nombre completo del cliente
   */
  getClientName(): string {
    if (this.data.cliente) {
      return `${this.data.cliente.nombres} ${this.data.cliente.apellidos}`;
    }
    return 'Cliente no especificado';
  }

  /**
   * Obtiene el color del estado de ánimo
   */
  getSentimentColor(sentiment: string): string {
    return this.transcriptionService.getSentimentColor(sentiment);
  }

  /**
   * Obtiene el icono del estado de ánimo
   */
  getSentimentIcon(sentiment: string): string {
    return this.transcriptionService.getSentimentIcon(sentiment);
  }

  /**
   * Traduce el estado de ánimo
   */
  translateSentiment(sentiment: string): string {
    return this.transcriptionService.translateSentiment(sentiment);
  }

  /**
   * Formatea la duración del audio
   */
  formatDuration(seconds: number): string {
    return this.transcriptionService.formatDuration(seconds);
  }

  /**
   * Formatea el tiempo de procesamiento
   */
  formatProcessingTime(seconds: number): string {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
    }
  }



  /**
   * Obtiene el color para la calidad del audio
   */
  getAudioQualityColor(score: number): string {
    if (score >= 0.8) return '#4caf50'; // Verde
    if (score >= 0.6) return '#ff9800'; // Naranja
    if (score >= 0.4) return '#f44336'; // Rojo
    return '#9e9e9e'; // Gris
  }

  /**
   * Obtiene la descripción de la calidad del audio
   */
  getAudioQualityDescription(score: number): string {
    if (score >= 0.8) return 'Excelente';
    if (score >= 0.6) return 'Buena';
    if (score >= 0.4) return 'Regular';
    if (score >= 0.2) return 'Mala';
    return 'Muy mala';
  }

  /**
   * Alterna la visualización de información avanzada
   */
  toggleAdvancedInfo(): void {
    this.showAdvancedInfo = !this.showAdvancedInfo;
  }

  /**
   * Copia la transcripción al portapapeles
   */
  copyTranscription(): void {
    if (this.transcriptionResult?.transcription) {
      navigator.clipboard.writeText(this.transcriptionResult.transcription).then(() => {
        this.showSuccess('Transcripción copiada al portapapeles');
      }).catch(() => {
        this.showError('Error al copiar la transcripción');
      });
    }
  }

  /**
   * Confirma si el usuario quiere sobrescribir la transcripción existente
   */
  private confirmOverwriteTranscription(): Promise<boolean> {
    return new Promise((resolve) => {
      const snackBarRef = this.snackBar.open(
        '⚠️ Este cliente ya tiene una transcripción. ¿Desea sobrescribirla?',
        'SÍ, SOBRESCRIBIR',
        {
          duration: 15000, // 15 segundos para decidir
          panelClass: ['warning-snackbar'],
          horizontalPosition: 'center',
          verticalPosition: 'top'
        }
      );

      // Si hace clic en el botón, confirma
      snackBarRef.onAction().subscribe(() => {
        resolve(true);
      });

      // Si se cierra sin acción, cancela
      snackBarRef.afterDismissed().subscribe((dismissedByAction) => {
        if (!dismissedByAction.dismissedByAction) {
          resolve(false);
        }
      });
    });
  }

  /**
   * Obtiene información de la transcripción existente para mostrar al usuario
   */
  getExistingTranscriptionInfo(): string {
    if (!this.verificationResult) {
      return '';
    }

    let info = `Cliente: ${this.verificationResult.nombreCliente || 'No especificado'}`;

    if (this.verificationResult.tieneTextoTranscripcion) {
      info += '\n✓ Tiene texto de transcripción';
    }

    if (this.verificationResult.tieneUrlDriveTranscripcion) {
      info += '\n✓ Tiene archivo en Google Drive';
    }

    return info;
  }

  /**
   * Abre la transcripción existente en Google Drive (si existe)
   */
  openExistingTranscription(): void {
    if (this.verificationResult?.urlDriveTranscripcion) {
      window.open(this.verificationResult.urlDriveTranscripcion, '_blank');
    }
  }

  /**
   * Reinicia el proceso para una nueva transcripción
   */
  resetTranscription(): void {
    this.currentStep = 'config';
    this.progress = 0;
    this.statusMessage = '';
    this.transcriptionResult = null;
    this.isProcessing = false;
    this.showExistingTranscriptionWarning = false;

    // Volver a verificar transcripción
    if (this.data.numeroMovil) {
      this.verificarTranscripcionExistente();
    }
  }

  /**
   * Guarda la transcripción en el cliente residencial
   */
  private saveTranscriptionToClient(response: any, callId: string): void {
    if (!this.data.numeroMovil) {
      console.warn('No se puede guardar la transcripción: número móvil no disponible');
      this.completeTranscription();
      return;
    }

    // Extraer el texto de transcripción de la respuesta
    let transcriptionText = '';
    if (response.transcription) {
      transcriptionText = response.transcription;
    } else {
      console.warn('No se encontró texto de transcripción en la respuesta');
      this.completeTranscription();
      return;
    }

    this.progress = 75;
    this.statusMessage = 'Guardando transcripción en el cliente...';

    const saveRequest: TranscriptionSaveRequest = {
      numeroMovil: this.data.numeroMovil,
      transcriptionText: transcriptionText,
      callId: callId,
      fileName: this.generateTranscriptionFileName(callId),
      forceOverwrite: this.verificationResult?.tieneTranscripcion || false
    };

    console.log('=== GUARDANDO TRANSCRIPCIÓN EN CLIENTE ===');
    console.log('Save request:', saveRequest);

    this.transcriptionSaveService.saveTranscription(saveRequest).subscribe({
      next: (saveResponse) => {
        console.log('=== RESPUESTA DEL GUARDADO DE TRANSCRIPCIÓN ===');
        console.log('Save response:', saveResponse);

        if (saveResponse.success) {
          this.showSuccess('Transcripción guardada en el cliente exitosamente');
          this.completeTranscription();
        } else if (saveResponse.error === 'TRANSCRIPTION_ALREADY_EXISTS') {
          // El cliente ya tiene una transcripción
          this.handleExistingTranscription(saveResponse, saveRequest);
        } else {
          console.error('Error al guardar transcripción:', saveResponse.error);
          this.showError('Error al guardar la transcripción: ' + (saveResponse.error || saveResponse.message));
          this.completeTranscription(); // Completar de todas formas
        }
      },
      error: (error) => {
        console.error('=== ERROR AL GUARDAR TRANSCRIPCIÓN ===');
        console.error('Error:', error);
        this.showError('Error al guardar la transcripción en el cliente');
        this.completeTranscription(); // Completar de todas formas
      }
    });
  }

  /**
   * Completa el proceso de transcripción
   */
  private completeTranscription(): void {
    this.currentStep = 'completed';
    this.progress = 100;
    this.statusMessage = 'Transcripción completada exitosamente';
    this.isProcessing = false;
  }

  /**
   * Maneja el caso cuando el cliente ya tiene una transcripción
   */
  private handleExistingTranscription(_saveResponse: any, originalRequest: any): void {
    this.progress = 90;
    this.statusMessage = 'Cliente ya tiene transcripción registrada';

    // Mostrar mensaje informativo con opciones usando MatSnackBar
    const snackBarRef = this.snackBar.open(
      'Este cliente ya tiene una transcripción registrada. ¿Desea sobrescribirla?',
      'SOBRESCRIBIR',
      {
        duration: 10000, // 10 segundos para que el usuario pueda decidir
        panelClass: ['warning-snackbar'],
        horizontalPosition: 'center',
        verticalPosition: 'top'
      }
    );

    // Manejar la acción del usuario
    snackBarRef.onAction().subscribe(() => {
      // Usuario quiere sobrescribir
      this.overwriteExistingTranscription(originalRequest);
    });

    // Si el snackbar se cierra sin acción, completar sin sobrescribir
    snackBarRef.afterDismissed().subscribe((dismissedByAction) => {
      if (!dismissedByAction.dismissedByAction) {
        // Usuario no quiere sobrescribir (cerró el snackbar sin hacer clic en SOBRESCRIBIR)
        this.showSuccess('Transcripción completada. El cliente mantiene su transcripción anterior.');
        this.completeTranscription();
      }
    });
  }

  /**
   * Sobrescribe la transcripción existente
   */
  private overwriteExistingTranscription(originalRequest: any): void {
    this.progress = 75;
    this.statusMessage = 'Sobrescribiendo transcripción existente...';

    const overwriteRequest = {
      ...originalRequest,
      forceOverwrite: true
    };

    console.log('=== SOBRESCRIBIENDO TRANSCRIPCIÓN EXISTENTE ===');
    console.log('Overwrite request:', overwriteRequest);

    this.transcriptionSaveService.saveTranscription(overwriteRequest).subscribe({
      next: (overwriteResponse) => {
        if (overwriteResponse.success) {
          this.showSuccess('Transcripción sobrescrita exitosamente');
          this.completeTranscription();
        } else {
          console.error('Error al sobrescribir transcripción:', overwriteResponse.error);
          this.showError('Error al sobrescribir la transcripción: ' + (overwriteResponse.error || overwriteResponse.message));
          this.completeTranscription();
        }
      },
      error: (error) => {
        console.error('Error al sobrescribir transcripción:', error);
        this.showError('Error al sobrescribir la transcripción');
        this.completeTranscription();
      }
    });
  }

  /**
   * Genera un nombre de archivo para la transcripción
   */
  private generateTranscriptionFileName(callId: string): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    let baseName = '';

    if (this.uploadedFile) {
      // Usar el nombre del archivo subido (sin extensión)
      baseName = this.uploadedFile.name.replace(/\.[^/.]+$/, '');
    } else if (this.data.file) {
      // Usar el nombre del archivo de Google Drive (sin extensión)
      baseName = this.data.file.name.replace(/\.[^/.]+$/, '');
    } else {
      baseName = 'audio';
    }

    return `transcripcion_${this.data.numeroMovil}_${baseName}_${callId}_${timestamp}.txt`;
  }

  /**
   * Cierra el diálogo
   */
  close(): void {
    this.dialogRef.close(this.transcriptionResult);
  }

  /**
   * Muestra mensaje de éxito
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  /**
   * Muestra mensaje de error
   */
  private showError(message: string): void {
    this.snackBar.open(message, 'Cerrar', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Maneja la subida de archivos por drag & drop
   */
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  /**
   * Maneja la selección de archivos desde el input
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFileSelection(input.files[0]);
    }
  }

  /**
   * Procesa el archivo seleccionado
   */
  handleFileSelection(file: File): void {
    // Validar que sea un archivo de audio
    if (!this.isValidAudioFile(file)) {
      this.snackBar.open('Por favor selecciona un archivo de audio válido (.mp3, .wav, .m4a, .ogg, .flac)', 'Cerrar', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // Validar tamaño del archivo (máximo 100MB)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      this.snackBar.open('El archivo es demasiado grande. Máximo permitido: 100MB', 'Cerrar', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    this.uploadedFile = file;
    console.log('Archivo seleccionado:', {
      name: file.name,
      size: file.size,
      type: file.type
    });
  }

  /**
   * Valida si el archivo es de audio
   */
  isValidAudioFile(file: File): boolean {
    const validTypes = [
      'audio/mpeg',
      'audio/wav',
      'audio/mp4',
      'audio/ogg',
      'audio/flac',
      'audio/x-m4a'
    ];

    const validExtensions = ['.mp3', '.wav', '.m4a', '.ogg', '.flac'];
    const fileName = file.name.toLowerCase();

    return validTypes.includes(file.type) ||
           validExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * Elimina el archivo seleccionado
   */
  removeUploadedFile(): void {
    this.uploadedFile = null;
  }

  /**
   * Formatea el tamaño del archivo
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Muestra mensaje de funcionalidad futura para comparar lead
   */
  compararLead(): void {
    // Importar SweetAlert2 dinámicamente para evitar problemas de bundle
    import('sweetalert2').then((Swal) => {
      Swal.default.fire({
        title: '🚀 Funcionalidad Futura',
        html: `
          <div style="text-align: left; padding: 20px;">
            <h4 style="color: #4f46e5; margin-bottom: 15px;">📊 Comparación de Lead</h4>
            <p style="margin-bottom: 10px;">Esta funcionalidad permitirá:</p>
            <ul style="margin-left: 20px; color: #6b7280;">
              <li>✨ Comparar la transcripción con datos del lead existente</li>
              <li>🔍 Detectar discrepancias en la información</li>
              <li>📈 Analizar la calidad de la llamada vs. datos registrados</li>
              <li>🎯 Sugerir actualizaciones automáticas del perfil</li>
              <li>📋 Generar reportes de consistencia</li>
            </ul>
            <div style="margin-top: 20px; padding: 15px; background: #f3f4f6; border-radius: 8px;">
              <strong style="color: #059669;">💡 Próximamente disponible</strong>
              <br>
              <small style="color: #6b7280;">Esta función estará disponible en futuras actualizaciones del sistema.</small>
            </div>
          </div>
        `,
        icon: 'info',
        iconColor: '#4f46e5',
        confirmButtonText: '¡Entendido!',
        confirmButtonColor: '#4f46e5',
        showCancelButton: false,
        width: '500px',
        customClass: {
          popup: 'swal-custom-popup',
          title: 'swal-custom-title'
        }
      });
    }).catch((error) => {
      console.error('Error loading SweetAlert2:', error);
      // Fallback con snackbar si SweetAlert2 no está disponible
      this.snackBar.open(
        '🚀 Funcionalidad "Comparar Lead" - Implementación a futuro',
        'Cerrar',
        {
          duration: 5000,
          panelClass: ['info-snackbar']
        }
      );
    });
  }
}
