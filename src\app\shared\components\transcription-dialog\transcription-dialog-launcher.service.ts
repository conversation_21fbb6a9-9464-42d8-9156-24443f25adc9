import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { TranscriptionDialogComponent, TranscriptionDialogData } from './transcription-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class TranscriptionDialogLauncherService {

  constructor(private dialog: MatDialog) {}

  /**
   * Abre el modal de transcripción para subir archivos de audio
   * @param options Opciones para configurar el modal
   */
  openTranscriptionDialog(options: {
    cliente?: {
      nombres: string;
      apellidos: string;
    };
    numeroMovil?: string;
    allowFileUpload?: boolean;
  }) {
    const dialogData: TranscriptionDialogData = {
      cliente: options.cliente,
      numeroMovil: options.numeroMovil,
      allowFileUpload: options.allowFileUpload || true
    };

    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '90vw',
      maxWidth: '800px',
      height: '90vh',
      maxHeight: '700px',
      disableClose: false,
      data: dialogData
    });

    return dialogRef.afterClosed();
  }

  /**
   * Abre el modal de transcripción con un archivo de Google Drive
   * @param file Archivo de Google Drive
   * @param options Opciones adicionales
   */
  openTranscriptionDialogWithGoogleDriveFile(
    file: any,
    options: {
      cliente?: {
        nombres: string;
        apellidos: string;
      };
      numeroMovil?: string;
    }
  ) {
    const dialogData: TranscriptionDialogData = {
      file: file,
      cliente: options.cliente,
      numeroMovil: options.numeroMovil,
      allowFileUpload: false
    };

    const dialogRef = this.dialog.open(TranscriptionDialogComponent, {
      width: '90vw',
      maxWidth: '800px',
      height: '90vh',
      maxHeight: '700px',
      disableClose: false,
      data: dialogData
    });

    return dialogRef.afterClosed();
  }
}
